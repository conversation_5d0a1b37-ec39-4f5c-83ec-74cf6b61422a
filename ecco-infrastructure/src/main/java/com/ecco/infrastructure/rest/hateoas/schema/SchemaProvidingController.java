package com.ecco.infrastructure.rest.hateoas.schema;

import com.ecco.infrastructure.rest.hateoas.ApiLinkTo;
import com.ecco.infrastructure.web.WebSlice;
import com.ecco.webApi.annotations.GetJsonSchema;
import com.fasterxml.jackson.module.jsonSchema.JsonSchema;
import com.fasterxml.jackson.module.jsonSchema.types.LinkDescriptionObject;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.hateoas.RepresentationModel;
import org.springframework.http.ResponseEntity;


import java.util.Arrays;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.context.request.ServletWebRequest;
import org.springframework.web.context.request.WebRequest;

import static org.springframework.hateoas.server.mvc.WebMvcLinkBuilder.methodOn;

/**
 * Base class for controllers which provide a schema on "/$schema/".
 * Should also implement {@link #getEntityTypeName()} to register this schema for lookup via
 * {@code SchemaController}.
 *
 * @since 05/01/16
 */
@WebSlice("api")
public abstract class SchemaProvidingController<Self extends SchemaProvidingController<Self>> {
    public static final String REL_DESCRIBED_BY = "describedby";
    public static final String REL_EDIT = "edit";

    protected final Logger log = LoggerFactory.getLogger(getClass());

    private ResourceSchemaCreator schemaCreator;

    @Autowired
    void configureSchemaCreator(ResourceSchemaCreator schemaCreator) {
        this.schemaCreator = schemaCreator;
    }

    public ResourceSchemaCreator getSchemaCreator() {
        return schemaCreator;
    }

    @GetJsonSchema("/$schema/")
    public abstract ResponseEntity<JsonSchema> describe(WebRequest request);

    public abstract String getEntityTypeName();

    protected void addDescribedByLink(RepresentationModel<?> resource) {
        var l = ApiLinkTo.linkToApi(self().describe(null)).withRel(REL_DESCRIBED_BY);
        resource.add(l);
    }

    /**
     * Convenience method to make {@link org.springframework.hateoas.server.mvc.WebMvcLinkBuilder} expressions more
     * concise e.g.
     * <pre>
     *     linkTo(self().describe())
     * </pre>
     * rather than
     * <pre>
     *     linkTo(methodOn(SomeController.class).describe())
     * </pre>
     *
     * @return {@code methodOn(getClass())}
     */
    public Self self() {
        //noinspection unchecked
        return (Self) methodOn((Class) getClass());
    }

    /**
     * Convenience function to update the links array in the schema with a copy of that array
     * with link appended.
     */
    protected void addALink(JsonSchema schema, LinkDescriptionObject link) {
        LinkDescriptionObject[] links = schema.asSimpleTypeSchema().getLinks();
        LinkDescriptionObject[] copy = Arrays.copyOf(links, links.length + 1);
        copy[links.length] = link;
        schema.asSimpleTypeSchema().setLinks(copy);
    }

    protected void cacheForXSecs(WebRequest request, int seconds) {
        ((ServletWebRequest) request).getResponse().addHeader("Cache-Control", "max-age=" + seconds);
    }
}
