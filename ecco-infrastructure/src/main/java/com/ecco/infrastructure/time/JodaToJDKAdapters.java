package com.ecco.infrastructure.time;

import lombok.NoArgsConstructor;
import org.jetbrains.annotations.Contract;
import org.joda.time.*;
import org.jspecify.annotations.Nullable;

import java.time.ZoneId;
import java.time.ZonedDateTime;

import static lombok.AccessLevel.PRIVATE;

@NoArgsConstructor(access = PRIVATE)
final public class JodaToJDKAdapters {

    public static final ZoneId UTC = ZoneId.of("UTC");


    @Contract("null -> null; !null -> !null") // returns null if arg is null
    public static java.time.@Nullable LocalDateTime localDateTimeToJDk(@Nullable LocalDateTime dateTime) {
        if (dateTime == null) return null;
        return java.time.LocalDateTime.ofInstant(instantFromJoda(dateTime.toDateTime(DateTimeZone.UTC)), UTC);
    }

    @Contract("null -> null; !null -> !null") // returns null if arg is null
    public static java.time.@Nullable LocalDate localDateToJDk(@Nullable LocalDate date) {
        if (date == null) return null;
        return java.time.LocalDate.ofInstant(instantFromJoda(date.toDateTimeAtStartOfDay(DateTimeZone.UTC)), UTC);
    }


    @Contract("null -> null; !null -> !null") // returns null if arg is null
    public static java.time.@Nullable LocalTime localTimeToJDk(@Nullable LocalTime time) {
        if (time == null) return null;
        return java.time.LocalTime.of(time.getHourOfDay(), time.getMinuteOfHour(), time.getSecondOfMinute());
    }

    @Nullable
    @Contract("null -> null; !null -> !null") // returns null if arg is null
    public static DateTime dateTimeToJoda(@Nullable ZonedDateTime dateTime) {
        if (dateTime == null) return null;
        return new DateTime(dateTime.toInstant().toEpochMilli());
    }

    @Nullable
    @Contract("null -> null; !null -> !null") // returns null if arg is null
    public static ZonedDateTime dateTimeToJdk(@Nullable DateTime dateTime) {
        if (dateTime == null) return null;
        return ZonedDateTime.ofInstant(instantFromJoda(dateTime.toInstant()), UTC);
    }

    @Nullable
    @Contract("null -> null; !null -> !null") // returns null if arg is null
    public static LocalDateTime localDateTimeToJoda(java.time.@Nullable LocalDateTime dateTime) {
        if (dateTime == null) return null;
        // as per https://stackoverflow.com/questions/33030163/easy-way-to-convert-java-8s-localdatetime-to-jodas-localdatetime
        return new LocalDateTime(dateTime.atZone(UTC)
                .toInstant()
                .toEpochMilli());
    }

    @Nullable
    @Contract("null -> null; !null -> !null") // returns null if arg is null
    public static LocalDate localDateToJoda(java.time.@Nullable LocalDate date) {
        if (date == null) return null;
        return new LocalDate(date.getYear(), date.getMonthValue(), date.getDayOfMonth());
    }

    public static LocalTime localTimeToJoda(java.time.@Nullable LocalTime time) {
        if (time == null) return null;
        return new LocalTime(time.getHour(), time.getMinute(), time.getSecond());
    }

    @Contract("null -> null; !null -> !null") // returns null if arg is null
    public static java.time.@Nullable Instant instantFromJoda(@Nullable ReadableInstant instant) {
        return instant == null ? null : java.time.Instant.ofEpochMilli(instant.getMillis());
    }

    @Nullable
    @Contract("null -> null; !null -> !null") // returns null if arg is null
    public static Instant instantToJoda(java.time.@Nullable Instant instant) {
        return instant == null ? null : Instant.ofEpochMilli(instant.toEpochMilli());
    }
}
