import {
    NumberToObjectMap,
    StringToObjectMap,
    timestamp,
    TimestampFormatters
} from "@eccosolutions/ecco-common";
import {Address} from "./contact-dto";
import {BLDG_SYMBOL} from "./building-dto";

export function isNumber(str: number | string) {
    // Abuse isFinite to eliminate groupBy having created keys "undefined" or "null"
    // [1,null,"null",undefined,"undefined","1"].filter(isFinite)
    // (3) [1, null, "1"]
    return isFinite(str as any as number);
}

/* this may work, but relies on the first element returning something - could be iffy
export function groupByNative<T>(data: T[], property: (obj: T) => number | string) {
    const result = (!data || data.length == 0) ? undefined : property(data[0]);
    return !result ? {} : isNumber(result)
        ? groupByNativeNumber(data, (obj: T) => property(obj) as number)
        : groupByNativeString(data, (obj: T) => property(obj) as string);
}
*/

export function groupByNativeNumber<T>(data: T[], property: (obj: T) => number | undefined) {
    const byId = data.reduce((acc, value) => {
        // Group initialization
        const n = property(value);
        if (n !== undefined) {
            if (!acc[n]) {
                acc[n] = [];
            }
            // Grouping
            acc[n].push(value);
        }
        return acc;
    }, {} as NumberToObjectMap<T[]>);
    return byId;
}

export function groupByNativeString<T>(data: T[], property: (obj: T) => string | undefined) {
    const byId = data.reduce((acc, value) => {
        // Group initialization
        const n = property(value);
        if (n !== undefined) {
            if (!acc[n]) {
                acc[n] = [];
            }
            // Grouping
            acc[n].push(value);
        }
        return acc;
    }, {} as StringToObjectMap<T[]>);
    return byId;
}

function notEmpty(entry: any) {
    return !!entry;
}

function commaAppendIfPresent(str: string): string {
    return str ? ", " + str : "";
}

// *** CLONE TON contact-dto.ts ***
export function streetAddress(address?: Address | undefined) {
    return !address ? null : address.address ? address.address.filter(notEmpty).join(", ") : "";
}

// NB see also addressAsHtml
// NB see also summariseAddress
export function fullAddress(address?: Address | undefined): string | null {
    return !address
        ? null
        : address.address
          ? streetAddress(address)!
                .concat(commaAppendIfPresent(address.town || ""))
                .concat(commaAppendIfPresent(address.postcode))
          : "";
}
export function fullResidenceAddress(
    residenceName?: string | undefined,
    address?: Address | undefined
): string | null {
    const adr = fullAddress(address);
    return residenceName ? (adr ? `${BLDG_SYMBOL} `.concat(adr) : null) : adr;
}

/**
 * Adapt to assumption that a local-datetime format is actually UTC by adding Z before parsing
 */
function parseUtcTimestamp(utc: string): timestamp.Timestamp {
    utc = utc.endsWith("Z") ? utc : utc + "Z";
    let parsedOrNull = timestamp.parseIso8601(utc);
    if (parsedOrNull === null) {
        throw new Error("Failed to parse timestamp: " + utc);
    }
    return parsedOrNull;
}

export const iso8601UtcToFormatLocalShortDate = (utc: string | null) =>
    utc === null ? null : TimestampFormatters.formatDeviceLocalShortDate(parseUtcTimestamp(utc));

export const iso8601UtcToFormatLocalShort = (utc: string) =>
    utc === null ? null : TimestampFormatters.formatDeviceLocalShort(parseUtcTimestamp(utc));


