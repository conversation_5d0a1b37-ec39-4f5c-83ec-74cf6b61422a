import {EccoDate, HateoasResource, LinkDto} from "@eccosolutions/ecco-common";
import {JSONSchema6TypeName} from "json-schema";
import {getGlobalApiClient} from "./global";
import {iso8601UtcToFormatLocalShort} from "./utils";

/** This interface must match the Java class com.fasterxml.jackson.module.jsonSchema.JsonSchema. */
export interface JsonSchemaDto extends HateoasResource {
    id?: string | undefined;
    $ref?: string | undefined;
    $schema?: string | undefined;
    required?: boolean | undefined;
    readonly?: boolean | undefined;
    description?: string | undefined;
    links: LinkDescriptionObjectDto[];
    type: ValueTypeSchemaDto[] | JSONSchema6TypeName; // Could be an array of possible schemas, or a string type name.
}

/** JsonSchemaDto with asXxxSchema() methods added on */
export interface JsonSchemaAdapter extends JsonSchemaDto {
    /** Promise for when loading of referenced schemas has finished */
    loaded?: Promise<any> | undefined;
    disallow: JsonSchemaAdapter[];
    extends: JsonSchemaAdapter[];
    asSimpleTypeSchema(): SimpleTypeSchemaDto;
    asContainerSchema(): ContainerTypeSchemaDto;
    asObjectSchema(): ObjectSchemaDto;
    asValueTypeSchema(): ValueTypeSchemaDto;
    asUnionTypeSchema(): UnionTypeSchemaDto;
}

export function isJsonSchemaDto(obj: any): obj is JsonSchemaDto {
    return obj.$schema !== undefined;
}

/**
 * Adds syntactic sugar to a JSON schema to avoid lots of casting.
 * Any methods declared in these interfaces will be implemented by passing a raw JSON object to this function.
 * Inspired by Jackson's JsonSchema class which has the same approach but using real type information rather than property values.
 */
export function implementInterface(dto: JsonSchemaDto): JsonSchemaAdapter {
    const apiClient = getGlobalApiClient(); // should be set in ServicesContextProvider as that provides the apiClient that we load the parent resources from

    const schema = dto as JsonSchemaAdapter;
    let nullFn = (name: string): any => {
        console.log('Failed to convert to ' + name + ': ' + JSON.stringify(schema));
        return null;
    };
    let identityFn = () => schema;

    schema.asSimpleTypeSchema = nullFn.bind(schema, 'simple type');
    schema.asContainerSchema = nullFn.bind(schema, 'container');
    schema.asObjectSchema = nullFn.bind(schema, 'object');
    schema.asValueTypeSchema = nullFn.bind(schema, 'value type');
    schema.asUnionTypeSchema = nullFn.bind(schema, 'union type');

    (schema.disallow || []).forEach(s => implementInterface(s));

    // see also https://github.com/cambridgeweblab/common-ui/blob/c7dfef636c3b40030dd40397efd2262171bc12c9/components/ca-form.html#L1460
    if (Array.isArray(schema.extends)) { // e.g. [{type: "string", $ref: "./groups"}, {type: "object", $ref: "data:...."}]
        schema.loaded = Promise.all(schema.extends.map((extSchemaData, i) => { // So we can wait on the promise before loading any forms
            const url = extSchemaData.$ref;
            if (url) {
                if (url.match(/^data:/)) { // decode data:
                    const pieces = url.split(",");
                    const body =
                        pieces[0]!.indexOf(";base64") > -1
                            ? atob(pieces[1]!)
                            : decodeURIComponent(pieces[1]!);
                    schema.extends[i] = JSON.parse(body);
                }
                else {
                    console.log('Fetching extended schema: ' + url);
                    return apiClient.get<JsonSchemaDto>(url)
                        .then(schemaData => schema.extends[i] = implementInterface(schemaData));
                }
            }
            implementInterface(schema.extends[i]!);
            return Promise.resolve(schema.extends[i]);
        }))
            .then(() => {console.info("finished loading extended schemas for: %o", schema)});
    }

    if (Array.isArray(schema.type)) {
        schema.asUnionTypeSchema = <(() => UnionTypeSchemaDto)>identityFn;
        (<UnionTypeSchemaDto>schema).type.forEach(s => implementInterface(s));
        return schema;
    }

    //noinspection FallThroughInSwitchStatementJS
    switch (schema.type) {
        case "string":
        case "number":
        case "integer":
        case "boolean":
            schema.asSimpleTypeSchema = <(() => SimpleTypeSchemaDto)>identityFn;
            schema.asValueTypeSchema = <(() => ValueTypeSchemaDto)>identityFn;
            break;
        case "object":
        {
            schema.asObjectSchema = <(() => ObjectSchemaDto)>identityFn;
            let objectSchema = schema.asObjectSchema();
            let properties = objectSchema.properties;
            if (properties) {
                objectSchema.sortedKeys = Object.keys(properties).sort((a, b) => {
                    return properties[a].id! < properties[b].id! ? -1 : 1;
                });
                objectSchema.sortedKeys.forEach(p => {
                    implementInterface(properties[p]!);
                });
            }
        }
        break;
        case "array":
            schema.asSimpleTypeSchema = <(() => SimpleTypeSchemaDto)>identityFn;
            schema.asContainerSchema = <(() => ContainerTypeSchemaDto)>identityFn;
            break;
        case "any":
        case "null":
            schema.asSimpleTypeSchema = <(() => SimpleTypeSchemaDto)>identityFn;
            break;
    }

    if (schema.links) { // but we have links elsewhere
        (schema.links || []).forEach(l => {
            if (l.targetSchema) implementInterface(l.targetSchema);
            if (l.schema) implementInterface(l.schema);
        });
    }

    return schema;
}

export interface UnionTypeSchemaDto extends SimpleTypeSchemaDto {
    type: SimpleTypeSchemaDto[];
}

export interface SimpleTypeSchemaDto extends JsonSchemaAdapter {
    default?: string | undefined;
    title: string;
    links: LinkDescriptionObjectDto[];
}

export interface LinkDescriptionObjectDto extends LinkDto {
    targetSchema?: JsonSchemaDto | undefined;
    method?: string | undefined;
    enctype?: string | undefined;
    /** A schema that defines the query parameters that can be added to the href.
     * See annotations on Spring Controller @RequestMapping methods */
    schema?: JsonSchemaAdapter | undefined;
    title?: string | undefined;
    mediaType?: string | undefined;
}

export interface ContainerTypeSchemaDto extends JsonSchemaAdapter {
    items?: SimpleTypeSchemaDto | undefined;
}

export type PropertySchemaDto = SimpleTypeSchemaDto | ValueTypeSchemaDto | UnionTypeSchemaDto;

export interface ObjectSchemaDto extends ContainerTypeSchemaDto {
    properties: {[name: string]: PropertySchemaDto};
    sortedKeys?: string[] | undefined; // This doesn't come from the server...
}

export interface ValueTypeSchemaDto extends SimpleTypeSchemaDto {
    enum?: string[] | undefined;
    /** See com.fasterxml.jackson.databind.jsonFormatVisitors.JsonValueFormat */
    format?:
        | "color"
        | "date-time"
        | "date"
        | "time"
        | "email"
        | "host-name"
        | "ip-address"
        | "ipv6"
        | "phone"
        | "regex"
        | "style"
        | "uri"
        | "utc-millisec"
        // ecco custom ones
        | "message-key"
        | undefined;
}

export function isUnionTypeSchema(propertySchema: SimpleTypeSchemaDto | ValueTypeSchemaDto | UnionTypeSchemaDto): propertySchema is UnionTypeSchemaDto {
    return Array.isArray(propertySchema.type);
}

/** Return a map we can use to look up properties for filtering */
export function getInstancesSchemaProperties(schema: ObjectSchemaDto): {[name: string]: PropertySchemaDto} {
    const instances = schema.links.find(l => l.rel == "instances");
    return instances ? instances.schema!.asObjectSchema().properties : {};
}

/** Provide a list of objects with key, title for rendering to a selection list or validating entries */
export function extractEnumEntries(propertySchema: PropertySchemaDto): {key: string; title: string;}[] {

    if (propertySchema.extends && propertySchema.extends.length > 0) {
        return extractEnumEntries(propertySchema.extends[0] as PropertySchemaDto)
    }

    if (isUnionTypeSchema(propertySchema)) {
        return propertySchema.type.map(entry => ({
            key: (entry as ValueTypeSchemaDto).enum![0]!,
            title: entry.title
        }));
    }

    const maybeEnum = (propertySchema as ValueTypeSchemaDto).enum;
    if (Array.isArray(maybeEnum)) {
        return maybeEnum.map(key => ({key: key, title: key}));
    }
    else if (propertySchema.type == "boolean") {
        // Provide a default if we didn't have an enum
        return [
            {key: "true", title: "yes"},
            {key: "false", title: "no"}
        ]
    }
    console.error(`Property schema for ${propertySchema.id} does not contain any enum entries: %o`, propertySchema);
    return [];
}

function booleanAsYesNo(value: boolean) {
    return value ? "yes" : "no";
}

function getArrayFormatter(arrayProperty: SimpleTypeSchemaDto) {
    return (values: any[]) => values.map(value => getPropertyFormatter(arrayProperty)!(value)).join(", ");
}

/** A useful default property formatter.  You could intercept things such as "object" for doing something special */
export function getPropertyFormatter(property: SimpleTypeSchemaDto): ((value: any) => string | null) | undefined {
    switch (property.type) {
        case "number":
        case "integer":
        case "null":
        case "any":
            return undefined;
        case "array":
            return getArrayFormatter(property.asContainerSchema().items!);
        case "boolean":
            return booleanAsYesNo;
        case "string":
            switch (property.asValueTypeSchema().format) {
                case "date":
                    return EccoDate.iso8601ToFormatShort;
                case "date-time":
                    return iso8601UtcToFormatLocalShort; // TODO: need an option to capture "secs mins hours days ago"
                case "time":
                    return undefined; // TODO: probably want to strip off secs and millisecs
                default:
                    return undefined;
            }
        case "object":
            return JSON.stringify;
        default:
            return undefined;
    }
}
