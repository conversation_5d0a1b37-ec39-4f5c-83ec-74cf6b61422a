import {HateoasResource, StringToObjectMap} from "@eccosolutions/ecco-common";
import {
    CircularProgress,
    debounceSearchRender,
    MUIDataTable,
    MUIDataTableColumnDef,
    MUIDataTableOptions,
    Typography
} from "@eccosolutions/ecco-mui";
import {
    extractEnumEntries,
    getInstancesSchemaProperties,
    getPropertyFormatter,
    ObjectSchemaDto,
    ResourceList
} from "ecco-dto";
import * as React from "react";
import {FC, ReactNode, useMemo} from "react";

interface Props {
    title?: string | undefined;

    /** null when loading */
    schema: ObjectSchemaDto | null;
    /** null when loading */
    resourceList: ResourceList<HateoasResource> | null;
    /** Array of field names for columns to show by default (default: display all) */
    displayFields?: string[] | undefined;
    /** Field names to allow filter on (default: all) */
    filterFields?: string[] | undefined;
    /** Callback to turn links into element(s) for actions (e.g. menu or buttons) */
    actionRenderer?: ((rowData: HateoasResource) => ReactNode) | undefined;

    /** Allow row click to be turned into an action based on a link (e.g. rel="edit" or rel="self") */
    onRowClick?: ((resource: HateoasResource) => void) | undefined;

    /** Map of values to filters to apply for each field */
    filters: StringToObjectMap<string[]>;

    /** Callback for change to filtering */
    onFilter: (queryParams: StringToObjectMap<string[]>) => void;

    /** Which page to show */
    page: number;

    /** Load a different page of data */
    onPage: (page: number) => void;

    /** Handle search text box - called with null when dismissed */
    onSearch?: ((searchText: string | null) => void) | undefined;

    /** Pass (back) in searchText - i.e. controlled component */
    searchText?: string | null | undefined;
}

function toWords(input: string) {
    return input.split(/(?=[A-Z])/).join(" ").toLowerCase().replace(/_/g, " ");
}

export const SchemaDataTable: FC<Props> = ({
    title,
    schema,
    resourceList,
    displayFields,
    filterFields,
    actionRenderer,
    onRowClick,
    onFilter,
    filters,
    page,
    onPage,
    onSearch,
    searchText,
    children
}) => {
    const columns = useMemo<MUIDataTableColumnDef[]>(() => {
        if (!schema) return [];

        const queryProperties = getInstancesSchemaProperties(schema);

        function toColumnDef(name: string): MUIDataTableColumnDef {
            const property = schema!.properties[name];
            console.debug(property);
            const filterNames = queryProperties[name]
                ? extractEnumEntries(queryProperties[name])
                : // TODO: We'll probably want a list to use title for the text
                  undefined;
            const propertyType = property?.type;
            return {
                name,
                label: property?.title ? toWords(property.title) : toWords(name),
                options: {
                    ...(property == null
                        ? {}
                        : {customBodyRender: getPropertyFormatter(property)!}),
                    /** Lozenge */
                    ...(filterNames && propertyType === "boolean"
                        ? {
                              customFilterListRender: key =>
                                  filterNames.find(f => f.key == key)!.title
                          }
                        : {}),
                    display: displayFields ? displayFields.indexOf(name) >= 0 : true,
                    filter: filterFields ? filterFields.indexOf(name) >= 0 : true,
                    filterList: filters[name]!,
                    ...(filterNames == null
                        ? {}
                        : {
                              filterOptions: {
                                  names: filterNames
                                      .filter(e => e.key != queryProperties[name]!.default)
                                      .map(e => e.key),
                                  fullWidth: false,
                                  renderValue: key => filterNames.find(f => f.key == key)!.title
                              }
                          }),
                    filterType: propertyType === "boolean" ? "checkbox" : "dropdown",
                    sort: false,
                    viewColumns: !!property // Only allow column to be viewed if data is available. Otherwise it's a columndef to specify filter (e.g. user groups)
                }
            };
        }

        // We want sorted keys plus any additional filter fields specified. These additional filterFields are expected
        // to be found in the
        const fields = schema.sortedKeys!;
        filterFields?.forEach(f => {
            if (fields.indexOf(f) < 0) fields.push(f);
        });
        const columnDefs: MUIDataTableColumnDef[] = fields.map(toColumnDef);

        if (actionRenderer) {
            const actionCol: MUIDataTableColumnDef = {
                name: "actions",
                label: "actions",
                options: {
                    empty: true,
                    filter: false,
                    sort: false,
                    customBodyRenderLite: (dataIndex, rowIndex) => {
                        return actionRenderer(resourceList!.data[rowIndex]!);
                    }
                }
            };
            columnDefs.push(actionCol);
        }

        console.debug(columnDefs);
        return columnDefs;
    }, [schema, displayFields, filterFields, actionRenderer, filters]);

    const tableOptions: MUIDataTableOptions = {
        count: resourceList ? resourceList.numItems : 0,
        customSearchRender: debounceSearchRender(1000)!,
        searchText: searchText!,
        /*searchOpen: !!searchText,*/
        page,
        serverSide: true, // This indicates that the provided data is for the page/filter etc TODO: Make this optional - if true, then we use links[rel="instances"].schema.properties
        rowsPerPage: resourceList ? resourceList.pageSize : 10,
        rowsPerPageOptions: [],
        ...(onRowClick && resourceList
            ? {onRowClick: (_rowData, rowMeta) => onRowClick(resourceList.data[rowMeta.rowIndex]!)}
            : {}),
        onChangePage: onPage,
        // onFilterChange: (changedColumn, filterList, type) => {
        //     console.info(`${changedColumn} ${filterList} ${type}`)
        // },
        onTableChange: (action, tableState) => {
            if (action == "search" && onSearch) {
                console.info("search: ", tableState.searchText);
                onSearch(tableState.searchText);
                return;
            }
            const queryParams: StringToObjectMap<string[]> = {};
            for (let i = 0; i < tableState.columns.length; i++) {
                if (tableState.filterList[i]!.length) {
                    queryParams[tableState.columns[i]!.name] = tableState.filterList[i]!;
                }
            }

            console.info(`onTableChange(${action} `, queryParams);
            if (action == "filterChange" || action == "resetFilters") {
                onFilter(queryParams);
            }
        },
        download: false,
        print: false,
        filter: !filterFields || filterFields.length > 0,
        sort: false,
        selectableRows: "none" // at the moment this defaults to delete
    };
    return (
        <div>
            <MUIDataTable
                columns={columns}
                title={
                    <Typography variant="h6">
                        {title || ""}
                        {!resourceList && (
                            <CircularProgress
                                size={24}
                                style={{marginLeft: 15, position: "relative", top: 4}}
                            />
                        )}
                    </Typography>
                }
                data={resourceList ? resourceList.data : [["Loading..."]]}
                options={tableOptions}
            />
            {children}
        </div>
    );
};
