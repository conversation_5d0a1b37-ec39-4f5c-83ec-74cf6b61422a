import {IdName, Nullable, SelectListOption} from "@eccosolutions/ecco-common";
import * as React from "react";

import {RateCardDto, RateCardEntryDto} from "ecco-dto";
import {Box, Grid} from "@eccosolutions/ecco-mui";
import {checkBox, dropdownList, numberInput} from "ecco-components-core";
import {useServicesContext} from "../ServicesContext";


interface Props {
    rateCard: RateCardDto;
    rateCardEntry: Nullable<RateCardEntryDto>;
    chargeTypesFixedTemporal: SelectListOption[];
    onChangeRateCardEntry: (rateCardEntry: Partial<Nullable<RateCardEntryDto>>) => void;
}

export function RateCardEntry(props: Props) {
    const {rateCardEntry, onChangeRateCardEntry, chargeTypesFixedTemporal} = props;

    const {sessionData} = useServicesContext();
    const unitOfMeasurements: IdName[] = sessionData?.getDto().unitOfMeasurements;

    const updatePartial = (update: Partial<Nullable<RateCardEntryDto>>) => {
        onChangeRateCardEntry({...rateCardEntry, ...update});
    };

    // in liquibase, fin_ratecardentries contains a rateCard
    //      id, version, rateCardId, matchingCategoryTypeId, chargeCategoryListDefId, matchingFactors, defaultEntry, chargeType, fixedCharge, unitMeasurementId, unitCharge, childRateCardEntryId, unitsToRepeatFor, disabled, units
    //      (1, 0, 1, null, null, '{}', false, 'TEMPORAL', null, 3, 2.86, null, null, false, 1);
    //      matchingCategoryTypeId - null
    //      chargeCategoryListDefId - null
    //      matchingFactors - empty
    //      defaultEntry - false
    //      chargeType/chargeTypeFixedTemporal - 3, TEMPORAL
    //      fixedCharge - null
    //      unitMeasurementId - 3
    //      unitCharge - 2.86
    //      childRateCardEntryId - null
    //      unitsToRepeatFor - null
    //      disabled - false
    //      units - 1
    //
    // BUILDING (see FinanceChargeCalculation:118)
    //  has an array [chargeNameId, chargeCategoryId]
    //  where the first combination is returned where chargeNameId matches the first rateCard's chargeNameId (see findChargeCategoryCombinationForRateCard)
    //  then chargeCategoryId is matched against an entry's matchChargeCategoryId
    //  on the RateCardEntry:
    //      matchingCategoryTypeId - match chargeCategoryId
    //      chargeCategoryListDefId - not used
    //      matchingFactors - not used
    //
    // ROTA (search chargeVisit)
    // the eventStatusRateId (from eventEntry) is matched against the entry's matchChargeCategoryId
    //      matchCategoryTypeId - the schedule's categoryId (for a demand schedule, this is appointmentTypeId)
    //      chargeCategoryListDefId - outcome of visit eventStatusRateId (set by evidence or a drop)
    //      matchingFactors - not used
    //
    let unitName = unitOfMeasurements.find(u => u.id == rateCardEntry.unitMeasurementId)?.name;
    unitName = unitName ? unitName + "s" : "units";

    return (
        <Box m={4}>
            <Grid container direction="row" justify="flex-start" alignItems="flex-start">
                <Grid container>
                    <Grid item xs={12}>
                        {/* matchCategoryTypeId, if needed, is a type of 'category' below */}
                        {dropdownList(
                            "category",
                            onChangeRateCardEntry,
                            rateCardEntry,
                            "matchingChargeCategoryId",
                            sessionData
                                ?.getListDefinitionEntriesByListName("eventStatusRateId") // TODO: We want more than just this list
                                .map(l => l.getDto()) || []
                        )}
                    </Grid>
                    <Grid item xs={12}>
                        {dropdownList(
                            "type",
                            onChangeRateCardEntry,
                            rateCardEntry,
                            "chargeTypeFixedTemporal",
                            chargeTypesFixedTemporal,
                            undefined,
                            undefined,
                            true
                        )}
                    </Grid>
                    {(rateCardEntry.chargeTypeFixedTemporal == "TEMPORAL" ||
                        rateCardEntry.chargeTypeFixedTemporal == "FIXED_TEMPORAL") && (
                        <>
                            <Grid item xs={12}>
                                {dropdownList(
                                    "measurement unit",
                                    onChangeRateCardEntry,
                                    rateCardEntry,
                                    "unitMeasurementId",
                                    unitOfMeasurements,
                                    undefined,
                                    undefined,
                                    true
                                )}
                            </Grid>

                            <Grid item xs={12}>
                                {numberInput(
                                    "unitCharge",
                                    "unit charge",
                                    newState => updatePartial({unitCharge: newState.unitCharge}),
                                    rateCardEntry,
                                    undefined,
                                    2
                                )}
                            </Grid>

                            <Grid item xs={12}>
                                {numberInput(
                                    "units",
                                    unitName,
                                    newState => updatePartial({units: newState.units}),
                                    rateCardEntry,
                                    undefined
                                )}
                            </Grid>
                        </>
                    )}
                    {(rateCardEntry.chargeTypeFixedTemporal == "FIXED" ||
                        rateCardEntry.chargeTypeFixedTemporal == "FIXED_TEMPORAL") && (
                        <>
                            <Grid item xs={12}>
                                {numberInput(
                                    "fixedCharge",
                                    "fixed charge",
                                    newState => updatePartial({fixedCharge: newState.fixedCharge}),
                                    rateCardEntry,
                                    undefined,
                                    2
                                )}
                            </Grid>
                        </>
                    )}
                    <Grid item xs={12}>
                        {checkBox(
                            "defaultEntry",
                            "default",
                            newState => updatePartial({defaultEntry: newState.defaultEntry}),
                            rateCardEntry
                        )}
                    </Grid>
                    <Grid item xs={12}>
                        {checkBox(
                            "disabled",
                            "disabled",
                            newState => updatePartial({disabled: newState.disabled}),
                            rateCardEntry
                        )}
                    </Grid>
                </Grid>
            </Grid>
        </Box>
    );
}
