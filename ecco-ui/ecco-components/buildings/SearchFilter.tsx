import * as React from "react";
import {
    Typo<PERSON>,
    TextField,
    InputAdornment,
    FormControlLabel,
    Grid,
    Checkbox,
    FormControl,
    InputLabel,
    Select,
    MenuItem
} from "@eccosolutions/ecco-mui";
import SearchIcon from "@material-ui/icons/Search";
import {OccupancyFilter} from "ecco-dto";
import {SortOption} from "../data/entityLoadHooks";

// New interface for checkbox-based occupancy filtering
export interface OccupancyCheckboxFilter {
    occupied: boolean;
    void: boolean;
}

interface SearchFilterProps {
    searchInput: string;
    onSearchInputChange: (value: string) => void;
    occupancyFilter: OccupancyCheckboxFilter;
    onOccupancyFilterChange: (filter: OccupancyCheckboxFilter) => void;
    sortOption: SortOption;
    onSortOptionChange: (option: SortOption) => void;
}

/**
 * Component for filtering buildings by search text and occupancy status
 */
export const SearchFilter: React.FC<SearchFilterProps> = ({
    searchInput,
    onSearchInputChange,
    occupancyFilter,
    onOccupancyFilterChange,
    sortOption,
    onSortOptionChange
}) => {
    const handleOccupiedChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        onOccupancyFilterChange({
            ...occupancyFilter,
            occupied: event.target.checked
        });
    };

    const handleVoidChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        onOccupancyFilterChange({
            ...occupancyFilter,
            void: event.target.checked
        });
    };

    return (
        <Grid container>
            <Grid item xs={12}>
                <TextField
                    fullWidth
                    variant="outlined"
                    placeholder="search by building name, id, or address..."
                    value={searchInput}
                    onChange={event => onSearchInputChange(event.target.value)}
                    InputProps={{
                        startAdornment: (
                            <InputAdornment position="start">
                                <SearchIcon />
                            </InputAdornment>
                        )
                    }}
                    style={{marginBottom: 16}}
                />
            </Grid>
            <Grid item xs={2}></Grid>
            <Grid item xs={4}>
                <Typography variant="subtitle2" gutterBottom>
                    current:
                </Typography>
                <div style={{marginBottom: 16}}>
                    <FormControlLabel
                        control={
                            <Checkbox
                                checked={occupancyFilter.occupied}
                                onChange={handleOccupiedChange}
                                name="occupied"
                            />
                        }
                        label="occupied"
                    />
                    <FormControlLabel
                        control={
                            <Checkbox
                                checked={occupancyFilter.void}
                                onChange={handleVoidChange}
                                name="void"
                            />
                        }
                        label="void"
                    />
                </div>
            </Grid>
            <Grid item xs={4}>
                <FormControl variant="outlined" style={{minWidth: 200}}>
                    <InputLabel>sort by</InputLabel>
                    <Select
                        value={sortOption}
                        onChange={event => onSortOptionChange(event.target.value as SortOption)}
                        label="sort by"
                    >
                        <MenuItem value="name">by building</MenuItem>
                        <MenuItem value="changes">by move in/on</MenuItem>
                    </Select>
                </FormControl>
            </Grid>
        </Grid>
    );
};
