import * as React from "react";

export async function whyDidYouRender(): Promise<void> {
    if (process.env.NODE_ENV === "development") {
        try {
            // Alternative approach: Try to patch React before WDYR gets to it
            const ReactModule = React as any;

            // Store original createElement if it exists and is writable
            //const originalCreateElement = ReactModule.createElement;

            // Check if createElement is writable
            const descriptor = Object.getOwnPropertyDescriptor(ReactModule, "createElement");
            if (descriptor && !descriptor.writable && !descriptor.set) {
                console.warn(
                    "React.createElement is read-only, skipping why-did-you-render initialization"
                );
                return;
            }

            await import("@welldone-software/why-did-you-render").then(wdyr =>
                wdyr.default(React, {
                    trackAllPureComponents: true,
                    // Disable problematic features
                    trackHooks: false,
                    trackExtraHooks: [],
                    // Only log, don't throw
                    onlyLogs: true,
                    // Reduce noise
                    logOnDifferentValues: false,
                    logOwnerReasons: false,
                    hotReloadBufferMs: 500
                })
            );
            console.log("why-did-you-render initialized successfully");
        } catch (error) {
            console.warn("Failed to initialize why-did-you-render:", error);
            console.log("Continuing without why-did-you-render...");
        }
    }
}
