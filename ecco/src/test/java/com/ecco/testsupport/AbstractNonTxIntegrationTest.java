package com.ecco.testsupport;

import com.ecco.config.repositories.ListDefinitionRepository;
import com.ecco.config.repositories.SoftwareModuleRepository;
import com.ecco.config.service.SettingsService;
import com.ecco.config.service.SoftwareModuleService;
import com.ecco.dao.ClickStreamRepository;
import com.ecco.dao.ReferralRepository;
import com.ecco.dao.EvidenceSupportWorkRepository;
import com.ecco.dom.TaskDefinitionNameIdMappings;
import com.ecco.evidence.TaskDefinitionNameIdResolver;
import com.ecco.evidence.repositories.ThreatActionRepository;
import com.ecco.infrastructure.bus.MessageBus;
import com.ecco.infrastructure.config.root.*;
import com.ecco.infrastructure.spring.data.QueryDslJpaEnhancedRepositoryImpl;
import com.ecco.security.repositories.GroupRepository;
import com.ecco.security.repositories.UserRepository;
import com.ecco.security.service.UserManagementServiceImpl;
import com.ecco.service.*;
import com.ecco.service.reports.ReportService;
import com.ecco.service.reports.ReportServiceImpl;
import com.ecco.serviceConfig.EntityRestrictionService;
import com.ecco.serviceConfig.repositories.*;
import com.ecco.serviceConfig.service.RepositoryBasedServiceTypeService;
import com.ecco.serviceConfig.service.ServiceTypeService;
import com.ecco.serviceConfig.service.SessionDataService;
import com.ecco.serviceConfig.service.SessionDataServiceImpl;
import com.ecco.test.entities.hibernate.ForeignKeyDisabling;
import com.ecco.test.support.*;
import com.github.springtestdbunit.DbUnitTestExecutionListener;
import com.ecco.calendar.core.CalendarService;
import com.ecco.security.acl.AclHandler;
import org.hibernate.Session;
import org.junit.Rule;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.aspectj.AnnotationBeanConfigurerAspect;
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory;
import org.springframework.beans.factory.wiring.BeanConfigurerSupport;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.support.MessageSourceAccessor;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.security.crypto.password.MessageDigestPasswordEncoder;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestExecutionListeners;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.support.DependencyInjectionTestExecutionListener;
import org.springframework.test.context.support.DirtiesContextTestExecutionListener;
import org.springframework.test.context.transaction.TransactionalTestExecutionListener;
import org.springframework.test.context.web.ServletTestExecutionListener;

import javax.annotation.PostConstruct;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import java.lang.reflect.Field;

import static org.mockito.Mockito.mock;

/**
 * Whilst this class is helpful for testing envers (because audits aren't created until tx is finishing)
 * this is not the best approach, and should be moved to an acceptance test - possibly using REST interface
 */
@RunWith(SpringJUnit4ClassRunner.class)
// For mysql, change to DEV and use jvm args -Ddb.schema and -Dliquibase=CREATE (we don't want to enable tests on production 'UPDATE')
@ActiveProfiles({Profiles.EMBEDDED, Profiles.TEST_FIXTURE})
@ContextConfiguration(
        initializers=EccoApplicationContextInitializer.class ,
        classes={InfrastructureConfig.class, AbstractNonTxIntegrationTest.ServiceConfig.class},
        loader = StackTraceSanitisingContextLoader.class)
@TestExecutionListeners({ServletTestExecutionListener.class, // Defaults plus ours
    DependencyInjectionTestExecutionListener.class,
    DirtiesContextTestExecutionListener.class,
    TransactionalTestExecutionListener.class,
    AspectBeanFactoryResettingListener.class,
    DbUnitTestExecutionListener.class, ForeignKeyDisabling.class // Last 2 for DbUnit stuff until we get rid of it
    })

// TODO suspect this still runs in tx!
// NB This highlights the concerns I've had in the past around IT tests using rollback - for which we used unitdb and unitils:
// NB READ! https://www.javacodegeeks.com/2011/12/spring-pitfalls-transactional-tests.html
// NB which came from https://stackoverflow.com/questions/27987097/disabling-transaction-on-spring-testng-test-method
// NB This is the same problem we were facing which meant we used non-tx: https://blog.frankel.ch/how-to-test-code-that-uses-envers/
public abstract class AbstractNonTxIntegrationTest {

    protected final Logger log = LoggerFactory.getLogger(getClass());

    @Configuration
    @EnableJpaRepositories(basePackageClasses = {
            ThreatActionRepository.class, ReferralRepository.class, EvidenceSupportWorkRepository.class,
            ServiceTypeRepository.class, OutcomeRepository.class, QuestionGroupRepository.class,
            UserRepository.class, ListDefinitionRepository.class
        },
        repositoryBaseClass=QueryDslJpaEnhancedRepositoryImpl.class
    )
    public static class ServiceConfig {

        @PersistenceContext
        EntityManager entityManager;

        @Autowired
        ProjectRepository projectRepository;

        @Autowired
        ServiceRepository serviceRepository;

        @Autowired
        ClickStreamRepository clickStreamRepository;

        @Autowired
        ServiceTypeRepository serviceTypeRepository;

        @Autowired
        ServiceTypeWorkflowRepository serviceTypeWorkflowRepository;

        @Autowired
        SoftwareModuleRepository softwareModuleRepository;

        @Autowired
        OutcomeRepository outcomeRepository;

        @Autowired
        QuestionGroupRepository questionGroupRepository;

        @Bean public EntityRestrictionService entityRestrictionService(ApplicationContext applicationContext) {
            return new EntityRestrictionServiceImpl(mock(AclHandler.class), serviceRepository, projectRepository, applicationContext);
        }

        @Bean public ReportService reportService() {
            return new ReportServiceImpl(clickStreamRepository);
        }

        @Bean public SoftwareModuleService softwareModuleService() {
            return new SoftwareModuleService(this.softwareModuleRepository);
        }

        @Bean public MessageSourceAccessor messageSourceAccessor() {
            return mock(MessageSourceAccessor.class);
        }

        @Bean public UserManagementServiceImpl userManagementService(MessageDigestPasswordEncoder encoder,
                                                                     MessageBus<ApplicationEvent> messageBus,
                                                                     SettingsService settingsService,
                                                                     GroupRepository groupRepository,
                                                                     SoftwareModuleService softwareModuleService) {
            return new UserManagementServiceImpl(encoder, entityManager, messageBus,
                    settingsService, groupRepository, softwareModuleService);
        }

        @Bean public CalendarService cosmoCalendarService() {
            return mock(CalendarService.class);
        }

        @Bean public MessageDigestPasswordEncoder passwordEncoder() {
            return new MessageDigestPasswordEncoder("SHA-1");
        }

        @Bean public EventServiceImpl eventService() {
            return new EventServiceImpl();
        }

        @Bean public ReviewService reviewService() {
            return new ReviewServiceImpl();
        }

        @Bean public SettingsService settingsService() {
            return new SettingsServiceImpl();
        }

        @Bean public SessionDataService sessionDataService(
                QuestionAnswerChoiceRepository qacRepository,
                QuestionAnswerFreeRepository questionAnswerFreeRepository,
                QuestionRepository questionRepository) {
            return new SessionDataServiceImpl(outcomeRepository, qacRepository, questionAnswerFreeRepository, questionGroupRepository,
                    questionRepository);
        }

        @Bean public ServiceTypeService serviceTypeService() {
            return new RepositoryBasedServiceTypeService(serviceTypeRepository, serviceTypeWorkflowRepository);
        }

        @Bean public TaskDefinitionService taskDefinitionService() {
            return new TaskDefinitionServiceImpl();
        }

        @Bean public TaskDefinitionNameIdResolver taskDefIdNameResolver(TaskDefinitionService taskDefinitionService) {
            return new com.ecco.service.TaskDefinitionNameIdResolver(taskDefinitionService);
        }

        @Bean public TaskDefinitionNameIdMappings taskDefNameIdMappings(TaskDefinitionNameIdResolver resolver) {
            return new TaskDefinitionNameIdMappings(resolver);
        }
    }

    @Rule
    public StackTraceSanitizingRule sanitizeRule = new StackTraceSanitizingRule();

    @Rule
    public TestTracerRule tracer = new TestTracerRule();

    @Rule
    public StatementStatsRule sqlStatsRule;

    @Autowired
    EnvironmentPropertiesConfig environmentPropertiesConfig;

    @Autowired
    protected StatementStatsInterceptor statementStats;

    @PersistenceContext
    protected EntityManager entityManager;

    protected Session getCurrentSession() {
        return (Session)entityManager;
    }

    @PostConstruct
    public void postConstruct() {
        sqlStatsRule = new StatementStatsRule(statementStats);
    }

    // @Before // Was used for debugging - committing at least once before killing off
    public void checkBeanFactoryContainsSettingService() throws Exception {
        AnnotationBeanConfigurerAspect aspect = AnnotationBeanConfigurerAspect.aspectOf();
//        System.err.println("aspect is " + aspect);
        BeanConfigurerSupport support = getProperty(aspect, "beanConfigurerSupport");
        ConfigurableListableBeanFactory beanFactory = getProperty(support, "beanFactory");
        System.err.println("BeanFactory for aspect is " + beanFactory);
//        SettingsService settingsSvc = beanFactory.getBean(SettingsService.class);
//        System.err.println("settingsService from aspect beanFactory is " + settingsSvc);
    }


    @SuppressWarnings("unchecked")
    private <T> T getProperty(Object object, String propertyName) throws Exception {
        Class<?> class1 = object.getClass();
        final Field f = class1.getDeclaredField(propertyName);
        f.setAccessible(true);
        return (T)f.get(object);
    }
}
