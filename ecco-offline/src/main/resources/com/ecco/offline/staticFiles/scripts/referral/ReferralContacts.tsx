import * as React from "react"
import {ClassAttributes, Fragment} from "react"
import {
    apiClient,
    CardData,
    CardSource,
    CommandForm,
    CommandSubform,
    withCommandForm,
    withSessionData
} from "ecco-components";
import {possiblyModalForm} from "ecco-components-core";
import {Observable} from "rxjs";
import {ServiceRecipientAssociatedContact} from "ecco-dto/referral-dto";
import {EccoDate, EccoDateTime} from "@eccosolutions/ecco-common";
import {CardsContainer} from "../components/CardsContainer";
import {AssociatedContactWizard} from "../contacts/AssociatedContactWizard";
import {
    Agency,
    ReferralDto,
    ServiceRecipientAjaxRepository,
    SessionData,
    SourceType
} from "ecco-dto";
import {Individual} from "ecco-dto/contact-dto";
import {mountWithServices} from "../offline/ServicesContextProvider";
import {CommandQueue, ServiceRecipientAssociatedContactCommand} from "ecco-commands";
import {flatMap, map} from "rxjs/operators";
import services = require("ecco-offline-data");
import {NewReferralWizard} from "./components/NewReferralWizard";
import {ClientListWithIncident} from "../clientdetails/components/ClientList";

/**
 * SINGLE ReferralContactEditor
 * TODO probably should load things we do in AgencyProfessionals with react-async instead
 */

export function getReferralContactEditorForm(serviceRecipientId: number,
                                             sessionData: SessionData,
                                             formRef: (c: ReferralContactEditor) => void) {
    return withCommandForm(commandForm =>
        <ReferralContactEditor
            ref={formRef}
            sessionData={sessionData}
            serviceRecipientId={serviceRecipientId}
            commandForm={commandForm}
        />
    );
}

// NB we only need 'contactId' but this makes it easy to pass through to AgencyProfessionals
interface EditorProps {
    sessionData: SessionData;
    serviceRecipientId: number;
}

interface EditorState {
    type: SourceType;
    agencyId: number;
    professionalId: number;
    individualId: number;
}

export class ReferralContactEditor extends CommandSubform<EditorProps, EditorState> {

    constructor(props) {
        super(props);
        this.state = {
            type: undefined,
            agencyId: undefined,
            professionalId: undefined,
            individualId: undefined
        };
    }

    emitChangesTo(commandQueue: CommandQueue) {
        if (this.state.type == "professional") {
            // Skip this for individual as we've done it when saving the individual
            const cmd = new ServiceRecipientAssociatedContactCommand("add", this.props.serviceRecipientId, this.state.professionalId || this.state.agencyId);
            commandQueue.addCommand(cmd);
        }
    }

    valid(): boolean {
        const tmp: Partial<ReferralDto> = {};
        tmp.referrerAgencyId = this.state.agencyId;
        tmp.referrerIndividualId = this.state.individualId;
        const errors = NewReferralWizard.validateSource(tmp, this.state.type);
        return Object.keys(errors).length == 0;
    }

    render() {
        return possiblyModalForm(
            "",
            false, true,
            () => this.props.commandForm.cancelForm(),
            () => this.props.commandForm.submitForm(),
            !this.valid(),
            false,

            <AssociatedContactWizard
                title={"new contact"}
                showSelf={false}
                sessionData={this.props.sessionData}
                serviceRecipientId={this.props.serviceRecipientId}
                sourceType={this.state.type}
                agencyId={this.state.agencyId}
                professionalId={this.state.professionalId}
                individualId={this.state.individualId}
                onSourceTypeChange={(sourceType: SourceType) => this.setState({type: sourceType})}
                onAgencyProfessionalChange={(agency: Agency, individual: Individual) => this.setState({
                    agencyId: agency?.contactId,
                    professionalId: individual?.contactId
                })}
                onIndividualChange={(individual: Individual) => this.setState({individualId: individual.contactId})}
            />);
    }
}

interface ReferralContactsProps extends ClassAttributes<ReferralContacts> {
    serviceRecipientId: number;
    sessionData: SessionData;
    printView: boolean;
}

interface ReferralContactsState {
    // TODO have all the referral contacts here, rather than reload
    showReferralContactForm: boolean;
    showLinkClientButton: boolean;
    showLinkClientForm: boolean;
}

export class ReferralContacts extends React.Component<ReferralContactsProps, ReferralContactsState> {
    private repository = new ServiceRecipientAjaxRepository(apiClient);

    constructor(props: ReferralContactsProps) {
        super(props);

        this.state = {
            showReferralContactForm: false,
            showLinkClientForm: false,
            showLinkClientButton: false
        }
    }

    public override componentDidMount() {
        // if chosen 'import', reload the data to set the first/last name
        this.repository.findOneServiceRecipientById(this.props.serviceRecipientId)
            .then(sr => {
                this.setState({showLinkClientButton: sr.prefix == "i"});
            })
    }

    override render() {

        const newContactAction = <div className="text-center">
                <a key={"newContact"}
                   className="btn btn-link"
                   onClick={() => {
                       this.setState({showReferralContactForm: true})
                   }}
                >{"add contact"}</a>
            </div>;

        const linkClientAction = this.state.showLinkClientButton && <div className="text-center">
            <a key={"linkClient"}
               className="btn btn-link"
               onClick={() => {
                   ClientListWithIncident.addIncidentClientReferralModal(this.props.serviceRecipientId, true, () => {}) /*reload*/
                   /*this.setState({showLinkClientForm: true})*/
               }}
            >{"add person supported"}</a>
        </div>;

        /*const linkClientForm = <ClientList
            sessionData={this.props.sessionData}
            subFormsAsModal={false}
            existingOnly={true}
            onChange={(client, isNew) => {}}
            selectedResultRender={true}
            selectedResultRenderer={client =>
                <ClientReferralsPopup
                    client={client}
                    existingOnly={true}
                    onSelectedReferral={(sessionData, referral) => {
                        console.debug("referral selected: %o", referral);
                        //cancelRef.current();
                        //onSelectedReferral(referral);
                        this.setState({showLinkClientForm: false})}
                    }
                />
            }
        />*/
        const referralContactForm = <CommandForm
                onCancel={() => this.setState({showReferralContactForm: false})}
                onFinished={() => this.setState({showReferralContactForm: false})}
                >
                {getReferralContactEditorForm(this.props.serviceRecipientId, this.props.sessionData, () => {})}
            </CommandForm>;

        return (
                <Fragment>
                    <div style={{fontSize: '16px'}}>{this.props.printView ? null :
                            <>
                                {linkClientAction}
                                {newContactAction}
                            </>
                    }</div>
                    {this.state.showReferralContactForm && referralContactForm}
                    {/*{this.state.showLinkClientForm && linkClientForm}*/}
                    {!this.state.showReferralContactForm && !this.state.showLinkClientForm &&
                        <CardsContainer sources={[new ReferralContactsSource(this.props.serviceRecipientId, this.props.printView)]}/>
                    }
                </Fragment>
        );
    }
}

class ReferralContactsSource implements CardSource {

    constructor(private srId: number, private printView: boolean) {}

    public getCards() {
        const referralsContactsQ = services.getReferralRepository().findAssociatedContactsByServiceRecipientId(this.srId);

        const contacts = Observable.fromPromise(referralsContactsQ).pipe(
            flatMap(a => a),
            map(dto => new ReferralContactCard(dto, this.printView)));

        return contacts;
        //return Observable.from([new ReferralContactCard(contacts)]);
    }
}

/*
export class ReferralContactCardGroup extends CardGroup<ReferralContactCard> {
    getPriority() {
        return EccoDateTime.nowUtc();
    }
}
*/

export class ReferralContactCard implements CardData {
    constructor(public dto: ServiceRecipientAssociatedContact, public printView: boolean) {
    }


    getKey() {
        return "contact-" + this.dto.contactId
    }

    getPriority() {
        // sort by archived at the bottom pushed down by 2000
        // followed by 'live' contacts - ordered by the date created (if a command exists), or contactId (if no command)
        // NB contactId would have to extend beyond 1051200000 minutes to beat 2000 years
        return this.dto.archived
            ? EccoDate.parseIso8601(this.dto.archived).toDateTimeMidnight().addYears(2000)
            : this.dto.created
                ? EccoDateTime.parseIso8601Utc(this.dto.created)
                : EccoDateTime.nowLocalTime().addMinutes(this.dto.contactId);
    }
}

// called from JSP - referralContactsFlow and 'contacts' tab in referral-contacts-control.tag
// This is the react version, showing the cards and the 'new contact'
// noinspection JSUnusedGlobalSymbols
export function ReferralContactsEnhance($element: $.JQuery, srId: number, printView = false) {
    mountWithServices(
        withSessionData(sd =>
            <ReferralContacts serviceRecipientId={srId} sessionData={sd} printView={printView}/>
        ),
        $element[0]);
}
