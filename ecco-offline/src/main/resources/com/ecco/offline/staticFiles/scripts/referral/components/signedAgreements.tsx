import * as React from "react"
import {ReactElement} from "react"
import {EccoDate, EccoDateTime} from "@eccosolutions/ecco-common";

import {
    CommandQueue,
    EditAgreement1Command,
    EditAgreement2Command,
    EditAgreement3Command,
    EditAgreement4Command,
    EditAgreement5Command,
    EditAgreement6<PERSON>ommand,
    EditAgreement7Command,
    EditAgreement8<PERSON>ommand,
    EditAgreement9Command,
    EditAgreement10Command,
    EditConsentCommand,
    EditDataProtectionCommand,
    EditSignedAgreementCommand
} from "ecco-commands";
import {
    AsyncServiceRecipientWithEntities,
    CommandSubform,
    getGuidanceUrlCallback,
    ServiceRecipientWithEntitiesContext,
    SignatureCapture,
    useCurrentServiceRecipientWithEntities,
    withCommandForm,
    withSessionData
} from "ecco-components";
import {datePickerInput,
    DomElementContainer,
    text<PERSON>rea,
    possiblyModalForm} from "ecco-components-core";
import {SessionData, TaskNames} from "ecco-dto";
import {ConsentSummaryFields} from "ecco-dto/referral-dto";
import {ServiceRecipientPlainFields} from "ecco-dto/service-recipient-dto";
import {Panel} from "react-bootstrap";
import {showInCommandForm} from "../../components/CommandForm";
import ViewSignatureControl from "../../controls/ViewSignatureControl";
import {AuditAgreement} from "ecco-components";
import {CustomSubform} from "./CustomForm";

export class SignedAgreementsForm {

    private static determineForm(taskName: string) {
        let frm: (sessionData: SessionData, serviceRecipientId: number, serviceAllocationId: number, taskHandle: string, printingPage: boolean, formRef: (c: SignedAgreement) => void) => JSX.Element;
        switch(taskName) {
            case TaskNames.dataProtection:
                frm = getDataProtectionForm;
                break;
            case TaskNames.consent:
                frm = getConsentForm;
                break;
            case TaskNames.agreement:
                frm = getAgreement1Form;
                break;
            case TaskNames.agreement2:
                frm = getAgreement2Form;
                break;
            case TaskNames.agreement3:
                frm = getAgreement3Form;
                break;
            case TaskNames.agreement4:
                frm = getAgreement4Form;
                break;
            case TaskNames.agreement5:
                frm = getAgreement5Form;
                break;
            case TaskNames.agreement6:
                frm = getAgreement6Form;
                break;
            case TaskNames.agreement7:
                frm = getAgreement7Form;
                break;
            case TaskNames.agreement8:
                frm = getAgreement8Form;
                break;
            case TaskNames.agreement9:
                frm = getAgreement9Form;
                break;
            case TaskNames.agreement10:
                frm = getAgreement10Form;
                break;
        }
        return frm;
    }

    /* // NB Doesn't load the right context - need WithEntities
    public static showInModal(serviceRecipientId: number, taskName: string, taskHandle: string, onCompleted?: () => void) {
        const frm = this.determineForm(taskName);
        showInCommandForm(
                withSessionData(sessionData =>
                    <ServiceRecipientLoadAndRender srId={serviceRecipientId}>{workflow =>
                        frm(sessionData, serviceRecipientId, workflow.serviceRecipient.serviceId, taskHandle, () => {})
                    }</ServiceRecipientLoadAndRender>
                )
            , onCompleted);
    }*/

    /**
     * Enhance the control on the page.
     */
    public static enhanceForPrinting($element: $.JQuery, onCompleted: () => void, srId: number, taskName: string, taskHandle?: string | undefined) {
        const frm = this.determineForm(taskName);
        showInCommandForm(
            withSessionData(sessionData =>
                <AsyncServiceRecipientWithEntities srId={srId}>
                    <AsyncServiceRecipientWithEntities.Resolved>
                        {(context: ServiceRecipientWithEntitiesContext) =>
                            frm(sessionData, srId, context.serviceRecipient.serviceAllocationId, taskHandle, true, () => {})
                        }
                    </AsyncServiceRecipientWithEntities.Resolved>
                </AsyncServiceRecipientWithEntities>
            )
            , onCompleted
            , $element[0]);
    }

}

interface FormOptions {
    createCommand(): EditSignedAgreementCommand;
    getTitle(): string;
    getSignedId(): string;
    getAgreementDate(): EccoDateTime;
    getAgreementStatus(): boolean | undefined;
    getServiceRecipientId(): number;
    getTaskName(): string;
    getReferralId(): number;
    isSplitAgreement(): boolean;
}

export class ConsentOptions implements FormOptions {

    private readonly agreementDate: EccoDateTime;
    private readonly agreementStatus?: boolean | undefined;

    constructor(private target: Partial<ConsentSummaryFields> & ServiceRecipientPlainFields, private taskHandle: string, private splitAgreement: boolean) {
        this.agreementDate = EccoDateTime.parseIso8601Utc(this.target.consentAgreementDate);
        this.agreementStatus = target.consentAgreementStatus;
    }

    getTaskName() {
        return TaskNames.consent;
    }

    getServiceRecipientId(): number {
        return this.target.serviceRecipientId;
    }

    getReferralId(): number {
        return undefined;
    }

    createCommand() {
        return new EditConsentCommand(this.target.serviceRecipientId, this.taskHandle);
    }

    getSignedId(): string {
        return this.target.consentSignedId;
    }

    getAgreementDate() {
        return this.agreementDate;
    }

    getAgreementStatus(): boolean {
        return this.agreementStatus;
    }

    isSplitAgreement(): boolean {
        return this.splitAgreement;
    }

    getTitle() { return 'consent'; }
}

export class DataProtectionOptions implements FormOptions {

    private readonly agreementDate: EccoDateTime;
    private readonly agreementStatus?: boolean | undefined;

    constructor(private target: Readonly<Partial<ConsentSummaryFields> & ServiceRecipientPlainFields>, private taskHandle: string, private splitAgreement: boolean) {
        this.agreementDate = EccoDateTime.parseIso8601Utc(this.target.dataProtectionAgreementDate);
        this.agreementStatus = target.dataProtectionAgreementStatus;
    }

    getTaskName() {
        return TaskNames.dataProtection;
    }

    getServiceRecipientId(): number {
        return this.target.serviceRecipientId;
    }

    getReferralId(): number {
        return undefined;// this.target.referralId;
    }

    createCommand() {
        return new EditDataProtectionCommand(this.target.serviceRecipientId, this.taskHandle);
    }

    getSignedId(): string {
        return this.target.dataProtectionSignedId;
    }

    getAgreementDate() {
        return this.agreementDate;
    }

    getAgreementStatus(): boolean | undefined {
        return this.agreementStatus;
    }

    isSplitAgreement(): boolean {
        return this.splitAgreement;
    }

    getTitle() { return 'data protection'; }
}

export class Agreement1Options implements FormOptions {

    protected agreementDate: EccoDateTime;
    protected agreementStatus?: boolean | undefined;
    protected agreementSignedId: string;
    protected title: string;

    constructor(protected target: Readonly<ConsentSummaryFields & ServiceRecipientPlainFields>, protected taskHandle: string, protected splitAgreement: boolean) {
        this.assign();
    }

    protected assign() {
        this.agreementDate = EccoDateTime.parseIso8601Utc(this.target.agreement1AgreementDate);
        this.agreementStatus = this.target.agreement1AgreementStatus;
        this.agreementSignedId = this.target.agreement1SignedId;
        this.title = "agreement";
    }

    createCommand() {
        return new EditAgreement1Command(this.target.serviceRecipientId, this.taskHandle);
    }

    getTaskName() {
        return TaskNames.agreement;
    }

    getTitle() { return this.title; }

    getServiceRecipientId(): number {
        return this.target.serviceRecipientId;
    }

    getReferralId(): number {
        return undefined;// this.target.referralId;
    }

    getSignedId(): string {
        return this.agreementSignedId;
    }

    getAgreementDate() {
        return this.agreementDate;
    }

    getAgreementStatus(): boolean {
        return this.agreementStatus;
    }

    isSplitAgreement(): boolean {
        return this.splitAgreement;
    }
}

export class Agreement2Options extends Agreement1Options {

    protected override assign() {
        this.agreementDate = EccoDateTime.parseIso8601Utc(this.target.agreement2AgreementDate);
        this.agreementStatus = this.target.agreement2AgreementStatus;
        this.agreementSignedId = this.target.agreement2SignedId;
        this.title = "agreement";
    }

    override getTaskName() {
        return TaskNames.agreement2;
    }

    override createCommand() {
        return new EditAgreement2Command(this.target.serviceRecipientId, this.taskHandle);
    }
}

export class Agreement3Options extends Agreement1Options {

    protected override assign() {
        this.agreementDate = EccoDateTime.parseIso8601Utc(this.target.agreement3AgreementDate);
        this.agreementStatus = this.target.agreement3AgreementStatus;
        this.agreementSignedId = this.target.agreement3SignedId;
        this.title = "agreement";
    }

    override getTaskName() {
        return TaskNames.agreement3;
    }

    override createCommand() {
        return new EditAgreement3Command(this.target.serviceRecipientId, this.taskHandle);
    }
}

export class Agreement4Options extends Agreement1Options {

    protected override assign() {
        this.agreementDate = EccoDateTime.parseIso8601Utc(this.target.agreement4AgreementDate);
        this.agreementStatus = this.target.agreement4AgreementStatus;
        this.agreementSignedId = this.target.agreement4SignedId;
        this.title = "agreement";
    }

    override getTaskName() {
        return TaskNames.agreement4;
    }

    override createCommand() {
        return new EditAgreement4Command(this.target.serviceRecipientId, this.taskHandle);
    }
}

export class Agreement5Options extends Agreement1Options {

    protected override assign() {
        this.agreementDate = EccoDateTime.parseIso8601Utc(this.target.agreement5AgreementDate);
        this.agreementStatus = this.target.agreement5AgreementStatus;
        this.agreementSignedId = this.target.agreement5SignedId;
        this.title = "agreement";
    }

    override getTaskName() {
        return TaskNames.agreement5;
    }

    override createCommand() {
        return new EditAgreement5Command(this.target.serviceRecipientId, this.taskHandle);
    }
}

export class Agreement6Options extends Agreement1Options {

    protected override assign() {
        this.agreementDate = EccoDateTime.parseIso8601Utc(this.target.agreement6AgreementDate);
        this.agreementStatus = this.target.agreement6AgreementStatus;
        this.agreementSignedId = this.target.agreement6SignedId;
        this.title = "agreement";
    }

    override getTaskName() {
        return TaskNames.agreement6;
    }

    override createCommand() {
        return new EditAgreement6Command(this.target.serviceRecipientId, this.taskHandle);
    }
}

export class Agreement7Options extends Agreement1Options {

    protected override assign() {
        this.agreementDate = EccoDateTime.parseIso8601Utc(this.target.agreement7AgreementDate);
        this.agreementStatus = this.target.agreement7AgreementStatus;
        this.agreementSignedId = this.target.agreement7SignedId;
        this.title = "agreement";
    }

    override getTaskName() {
        return TaskNames.agreement7;
    }

    override createCommand() {
        return new EditAgreement7Command(this.target.serviceRecipientId, this.taskHandle);
    }
}

export class Agreement8Options extends Agreement1Options {

    protected override assign() {
        this.agreementDate = EccoDateTime.parseIso8601Utc(this.target.agreement8AgreementDate);
        this.agreementStatus = this.target.agreement8AgreementStatus;
        this.agreementSignedId = this.target.agreement8SignedId;
        this.title = "agreement";
    }

    override getTaskName() {
        return TaskNames.agreement8;
    }

    override createCommand() {
        return new EditAgreement8Command(this.target.serviceRecipientId, this.taskHandle);
    }
}

export class Agreement9Options extends Agreement1Options {

    protected override assign() {
        this.agreementDate = EccoDateTime.parseIso8601Utc(this.target.agreement9AgreementDate);
        this.agreementStatus = this.target.agreement9AgreementStatus;
        this.agreementSignedId = this.target.agreement9SignedId;
        this.title = "agreement";
    }

    override getTaskName() {
        return TaskNames.agreement9;
    }

    override createCommand() {
        return new EditAgreement9Command(this.target.serviceRecipientId, this.taskHandle);
    }
}

export class Agreement10Options extends Agreement1Options {

    protected override assign() {
        this.agreementDate = EccoDateTime.parseIso8601Utc(this.target.agreement10AgreementDate);
        this.agreementStatus = this.target.agreement10AgreementStatus;
        this.agreementSignedId = this.target.agreement10SignedId;
        this.title = "agreement";
    }

    override getTaskName() {
        return TaskNames.agreement10;
    }

    override createCommand() {
        return new EditAgreement10Command(this.target.serviceRecipientId, this.taskHandle);
    }
}

export function getConsentForm(sessionData: SessionData, serviceRecipientId: number, serviceAllocationId: number, taskHandle: string, printingPage: boolean, formRef: (c: SignedAgreement) => void) {
    return <AgreementForm sessionData={sessionData} serviceRecipientId={serviceRecipientId} serviceAllocationId={serviceAllocationId} taskName={TaskNames.consent} taskHandle={taskHandle} optionsResolver={(sr, taskHandle, splitAgreement) => new ConsentOptions(sr, taskHandle, splitAgreement)} printingPage={printingPage} formRef={formRef} />;
}
export function getDataProtectionForm(sessionData: SessionData, serviceRecipientId: number, serviceAllocationId: number, taskHandle: string, printingPage: boolean, formRef: (c: SignedAgreement) => void) {
    return <AgreementForm sessionData={sessionData} serviceRecipientId={serviceRecipientId} serviceAllocationId={serviceAllocationId} taskName={TaskNames.dataProtection} taskHandle={taskHandle} optionsResolver={(sr, taskHandle, splitAgreement) => new DataProtectionOptions(sr, taskHandle, splitAgreement)} printingPage={printingPage} formRef={formRef} />;
}
export function getAgreement1Form(sessionData: SessionData, serviceRecipientId: number, serviceAllocationId: number, taskHandle: string, printingPage: boolean, formRef: (c: SignedAgreement) => void) {
    return <AgreementForm sessionData={sessionData} serviceRecipientId={serviceRecipientId} serviceAllocationId={serviceAllocationId} taskName={TaskNames.agreement} taskHandle={taskHandle} optionsResolver={(sr, taskHandle, splitAgreement) => new Agreement1Options(sr, taskHandle, splitAgreement)} printingPage={printingPage} formRef={formRef} />;
}
export function getAgreement2Form(sessionData: SessionData, serviceRecipientId: number, serviceAllocationId: number, taskHandle: string, printingPage: boolean, formRef: (c: SignedAgreement) => void) {
    return <AgreementForm sessionData={sessionData} serviceRecipientId={serviceRecipientId} serviceAllocationId={serviceAllocationId} taskName={TaskNames.agreement2} taskHandle={taskHandle} optionsResolver={(sr, taskHandle, splitAgreement) => new Agreement2Options(sr, taskHandle, splitAgreement)} printingPage={printingPage} formRef={formRef} />;
}
export function getAgreement3Form(sessionData: SessionData, serviceRecipientId: number, serviceAllocationId: number, taskHandle: string, printingPage: boolean, formRef: (c: SignedAgreement) => void) {
    return <AgreementForm sessionData={sessionData} serviceRecipientId={serviceRecipientId} serviceAllocationId={serviceAllocationId} taskName={TaskNames.agreement3} taskHandle={taskHandle} optionsResolver={(sr, taskHandle, splitAgreement) => new Agreement3Options(sr, taskHandle, splitAgreement)} printingPage={printingPage} formRef={formRef} />;
}
export function getAgreement4Form(sessionData: SessionData, serviceRecipientId: number, serviceAllocationId: number, taskHandle: string, printingPage: boolean, formRef: (c: SignedAgreement) => void) {
    return <AgreementForm sessionData={sessionData} serviceRecipientId={serviceRecipientId} serviceAllocationId={serviceAllocationId} taskName={TaskNames.agreement4} taskHandle={taskHandle} optionsResolver={(sr, taskHandle, splitAgreement) => new Agreement4Options(sr, taskHandle, splitAgreement)} printingPage={printingPage} formRef={formRef} />;
}
export function getAgreement5Form(sessionData: SessionData, serviceRecipientId: number, serviceAllocationId: number, taskHandle: string, printingPage: boolean, formRef: (c: SignedAgreement) => void) {
    return <AgreementForm sessionData={sessionData} serviceRecipientId={serviceRecipientId} serviceAllocationId={serviceAllocationId} taskName={TaskNames.agreement5} taskHandle={taskHandle} optionsResolver={(sr, taskHandle, splitAgreement) => new Agreement5Options(sr, taskHandle, splitAgreement)} printingPage={printingPage} formRef={formRef} />;
}
export function getAgreement6Form(sessionData: SessionData, serviceRecipientId: number, serviceAllocationId: number, taskHandle: string, printingPage: boolean, formRef: (c: SignedAgreement) => void) {
    return <AgreementForm sessionData={sessionData} serviceRecipientId={serviceRecipientId} serviceAllocationId={serviceAllocationId} taskName={TaskNames.agreement6} taskHandle={taskHandle} optionsResolver={(sr, taskHandle, splitAgreement) => new Agreement6Options(sr, taskHandle, splitAgreement)} printingPage={printingPage} formRef={formRef} />;
}
export function getAgreement7Form(sessionData: SessionData, serviceRecipientId: number, serviceAllocationId: number, taskHandle: string, printingPage: boolean, formRef: (c: SignedAgreement) => void) {
    return <AgreementForm sessionData={sessionData} serviceRecipientId={serviceRecipientId} serviceAllocationId={serviceAllocationId} taskName={TaskNames.agreement7} taskHandle={taskHandle} optionsResolver={(sr, taskHandle, splitAgreement) => new Agreement7Options(sr, taskHandle, splitAgreement)} printingPage={printingPage} formRef={formRef} />;
}
export function getAgreement8Form(sessionData: SessionData, serviceRecipientId: number, serviceAllocationId: number, taskHandle: string, printingPage: boolean, formRef: (c: SignedAgreement) => void) {
    return <AgreementForm sessionData={sessionData} serviceRecipientId={serviceRecipientId} serviceAllocationId={serviceAllocationId} taskName={TaskNames.agreement8} taskHandle={taskHandle} optionsResolver={(sr, taskHandle, splitAgreement) => new Agreement8Options(sr, taskHandle, splitAgreement)} printingPage={printingPage} formRef={formRef} />;
}
export function getAgreement9Form(sessionData: SessionData, serviceRecipientId: number, serviceAllocationId: number, taskHandle: string, printingPage: boolean, formRef: (c: SignedAgreement) => void) {
    return <AgreementForm sessionData={sessionData} serviceRecipientId={serviceRecipientId} serviceAllocationId={serviceAllocationId} taskName={TaskNames.agreement9} taskHandle={taskHandle} optionsResolver={(sr, taskHandle, splitAgreement) => new Agreement9Options(sr, taskHandle, splitAgreement)} printingPage={printingPage} formRef={formRef} />;
}
export function getAgreement10Form(sessionData: SessionData, serviceRecipientId: number, serviceAllocationId: number, taskHandle: string, printingPage: boolean, formRef: (c: SignedAgreement) => void) {
    return <AgreementForm sessionData={sessionData} serviceRecipientId={serviceRecipientId} serviceAllocationId={serviceAllocationId} taskName={TaskNames.agreement10} taskHandle={taskHandle} optionsResolver={(sr, taskHandle, splitAgreement) => new Agreement10Options(sr, taskHandle, splitAgreement)} printingPage={printingPage} formRef={formRef} />;
}

interface AgreementFormProps {
    sessionData: SessionData;
    serviceRecipientId: number;
    serviceAllocationId: number;
    taskName: string;
    taskHandle: string;
    optionsResolver: (sr: (ConsentSummaryFields & ServiceRecipientPlainFields), taskHandle: string, splitAgreement: boolean) => FormOptions;
    printingPage: boolean;
    formRef: (c: SignedAgreement) => void;
}

function AgreementForm(props: AgreementFormProps): JSX.Element | null {
    const {resolved, reload} = useCurrentServiceRecipientWithEntities()
    const splitAgreement = props.sessionData.getServiceTypeByServiceCategorisationId(resolved.serviceRecipient.serviceAllocationId).taskDefinitionSettingHasFlag(props.taskName,  "splitAgreement", "y");

    const guidanceFormDefinitionUuid = resolved.serviceRecipient.configResolver.getServiceType().getTaskDefinitionSetting(props.taskName, "guidanceFormDefinition");
    const guidanceCallback = guidanceFormDefinitionUuid ? getGuidanceUrlCallback(guidanceFormDefinitionUuid) : undefined;

    return withCommandForm(commandForm => {
            const options = props.optionsResolver(resolved.serviceRecipient, props.taskHandle, splitAgreement);
            const modal = !props.printingPage;
            const cancel = modal ? () => commandForm.cancelForm() : null;
            const save = modal ? () => commandForm.submitForm().then(reload) : null;
            return possiblyModalForm(
                options.getTitle(),
                modal, true,
                cancel,
                save,
                false,
                true, // ignore the save buttons, just have close (save is done by accepting the agreement)
                <>
                    <CustomSubform serviceRecipientId={props.serviceRecipientId}
                                   readOnly={props.printingPage || !!options.getAgreementDate()}
                                   historyLink={!props.printingPage}
                                   printableLink={!props.printingPage}
                                   taskName={props.taskName}
                                   taskNameGroup={props.taskName}
                                   taskHandle={props.taskHandle}
                                   commandForm={commandForm}
                    />
                    <br/>
                    <SignedAgreement
                        ref={props.formRef}
                        options={options}
                        readOnly={props.printingPage}
                        commandForm={commandForm}
                        onChange={() => commandForm.submitForm().then(reload)}
                    />
                </>,
                undefined,
                guidanceCallback
            );
        }
    );
}

interface Props extends React.ClassAttributes<SignedAgreement> {
    onChange?: ((form: SignedAgreement) => void) | undefined;
    options: FormOptions;
    readOnly: boolean;
}

interface State {
    agreementDate: EccoDate;
    accepted: boolean;
    signedId: string;
    reset: boolean;
    resetComment: string;
    currentSignatureData: string;
}

/** Used via router, mapped at: nav/r/referrals/{rid}/tasks/dataProtection
 *  Responsibilities: Show a the form for a given referral state, and allow updated
 *  state to be read */
export class SignedAgreement extends CommandSubform<Props, State> {

    private setter = state => this.setState(state);

    constructor(props) {
        super(props);

        // map the incoming instant to a local interpretation
        const agreementDateLocal = props.options.getAgreementDate()
            // CONVERT the EccoDateTime UTC into a local datetime and then extract the EccoDate (so that May 4th 23:00:00 UTC during BST is May 5th)
            // NB the underlying jsDate object still has to exist - showing as 01:00 when hovering in browser debug - which is okay, since time.ts is designed to ignore timezones
            ? props.options.getAgreementDate().toLocalEccoDate()
            : EccoDate.todayLocalTime();
        this.state = {
            agreementDate: agreementDateLocal,
            accepted: props.options.getAgreementStatus(),
            signedId: props.options.getSignedId(),
            reset: false,
            resetComment: null,
            currentSignatureData: null,
        };
    }

    emitChangesTo(commandQueue: CommandQueue) {
        if (this.state.reset) {
            this.emitResetTo(commandQueue);
        } else {
            this.emitAgreementTo(commandQueue);
        }
    }

    private emitResetTo(commandQueue: CommandQueue) {
        const cmd = this.props.options.createCommand()
            .withComment(this.state.resetComment)
            // NB agreementStatus as accept/reject/reset was considered, but reset is a transient status
            .withReset();
        commandQueue.addCommand(cmd);
    }

    public emitAgreementTo(commandQueue: CommandQueue) {
        const accepted = this.props.options.isSplitAgreement() ? null : this.getAccepted();
        const newSignature = this.state.currentSignatureData != null;

        const agreementDateLocal = this.state.agreementDate || EccoDate.todayLocalTime();
        // CONVERT from local EccoDate to UTC EcoDateTime
        // NB this becomes the day before at 23:00:00 (in BST) because that is the UTC for the local's start of the day
        const agreementDateUtc = EccoDateTime.fromUtcJsDate(agreementDateLocal.toUtcJsDate());

        const cmd = this.props.options.createCommand()
            .changeAgreementDate(this.props.options.getAgreementDate(), agreementDateUtc)
            .changeAgreementStatus(this.props.options.getAgreementStatus(), accepted);
        if (newSignature) {
            cmd.changeSignedDate(null, EccoDateTime.nowUtc());
            cmd.withSignature(this.state.currentSignatureData);
        }
        commandQueue.addCommand(cmd);
    }

    private handleClick(accept: boolean) {
        this.setState({accepted: accept},
            () => this.props.onChange && this.props.onChange(this) // has to be after so get new state in callback
        );
    };

    private handleReset() {
        this.props.onChange && this.props.onChange(this);
    };

    private handleCompleteClick = () => this.handleClick(null);
    private handleAcceptClick = () => this.handleClick(true);
    private handleRejectClick = () => this.handleClick(false);

    public getAccepted(): boolean {
        return this.state.accepted;
    }

    public getSignatureData() {
        return this.state.currentSignatureData;
    }

    updateSignatureData(sigData: string) {
        this.setState({
            currentSignatureData: sigData
        })
    }

    render() {
        const hasAgreement = this.props.options.getAgreementDate() != null;

        const summaryElement = hasAgreement ? <Panel>
                <div className='text-center'>
                    <AuditAgreement agreementDate={this.props.options.getAgreementDate().formatIso8601()}
                                                            agreementSignedId={this.props.options.getSignedId()}
                                                            agreementStatus={this.props.options.getAgreementStatus()} />
                    {!this.props.readOnly &&
                        <a id="reset"
                           className='btn btn-link'
                           onClick={() => {this.setState({reset: true})}}>
                            reset
                        </a>
                    }
                </div>
            </Panel>
            : null;

        const resetComment = <Panel>
            <div className='text-center'>
                <a id="resetCancel"
                   className='btn btn-link'
                   onClick={() => {this.setState({reset: false})}}>
                    cancel reset
                </a>
            </div>
            {textArea("resetComment", "reset reason", this.setter, this.state)}
            <a id="reset"
               className='btn btn-link'
               onClick={() => this.handleReset()}>
                reset
            </a>
        </Panel>;

        const signLater = (hasAgreement && !this.props.options.getSignedId() && !this.props.readOnly) ? <>
                <div style={{paddingTop: "25px", paddingBottom: "75px"}} className="text-center">
                    <SignatureCapture callback={(sigData) => this.updateSignatureData(sigData)}/>
                    <br/>
                    <br/>
                    <a id="acceptOrReject"
                       className='btn btn-link'
                       onClick={this.props.options.getAgreementStatus() ? this.handleAcceptClick : this.handleRejectClick}>
                        save
                    </a>
                </div>
            </>
            : null;

        const complete = <a  id="accept"
                             className='btn btn-link'
                             onClick={this.handleCompleteClick}>
                            complete
                        </a>;

        const acceptOrReject = <>
            <a  id="accept"
                className='btn btn-link'
                onClick={this.handleAcceptClick}>
                accept
            </a>
            <p> or </p>
            <a id="reject"
               className='btn btn-link'
               onClick={this.handleRejectClick}>
            reject
            </a>
        </>;

        const decideAndSignElement = (!hasAgreement && !this.props.readOnly) ? <>
            <div style={{width: 240}} className="center-block">
                {datePickerInput("agreementDate", "date of agreement", this.setter, this.state, false, false)}
            </div>
            <div style={{paddingTop: "5px", paddingBottom: "75px"}} className="text-center">
                <SignatureCapture callback={(sigData) => this.updateSignatureData(sigData)}/>
                <br/><br/>
                {this.props.options.isSplitAgreement() ? complete : acceptOrReject}
            </div>
            </>
            : null;

        let SignatureAsView: ReactElement = null;
        if (this.state.signedId) {
            const ctl = ViewSignatureControl.showSignatureInDomElement(this.state.signedId);
            SignatureAsView = <DomElementContainer content={ctl.domElement()}/>;
        }

        return (<div className='row'>
                <div className='col-xs-10 col-xs-offset-1'>
                    {!this.state.reset
                        ? <>
                            {summaryElement}
                            {SignatureAsView}
                            {decideAndSignElement}
                            {signLater}
                        </>
                        : resetComment
                    }
                </div>
            </div>
        );
    }
}
