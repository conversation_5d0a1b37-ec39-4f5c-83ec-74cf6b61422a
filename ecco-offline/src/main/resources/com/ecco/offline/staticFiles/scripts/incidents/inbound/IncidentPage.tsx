import {
    AppBar,
    Toolbar,
} from "@eccosolutions/ecco-mui";

import {applicationRootPath, resourceRootPath} from "application-properties";
import {EccoTheme} from "ecco-components-core";
import * as React from "react";
import * as ReactDom from "react-dom";
import {Route, Switch} from "react-router";
import {BrowserRouter} from "react-router-dom";
import {Wizard} from "./IncidentComponent";

//type RouteParams = {serviceCategorisationId?: string};

const leftIcon =
    <img src={resourceRootPath + "themes/ecco/images/logo_white.png"} height="48"/>;

// see AppBarBase
const orangeAppBar = "#A47C13FF";

export function IncidentComponent() {

    const search = window.location.search;
    const params = new URLSearchParams(search);
    //const serviceCategorisationId = useParams<RouteParams>().serviceCategorisationId;
    const serviceCategorisationId = params.get("svcCatId");

    return (
        <div>
            <AppBar
                position="fixed"
                style={{top: 0}}
            >
                <Toolbar
                    style={{
                        backgroundColor: orangeAppBar
                    }}>
                    {leftIcon}
                </Toolbar>
            </AppBar>
            <div style={{marginTop: 64}}>
                <Wizard schemaUri="incidents/$schema/" serviceCategorisationId={serviceCategorisationId ? parseInt(serviceCategorisationId) : null}/>
                {/*<Wizard schema={schema}/>*/}
            </div>
        </div>
    );
}

ReactDom.render((
    <EccoTheme prefix="refer">
        <BrowserRouter basename={applicationRootPath.substr(0, applicationRootPath.length - 1)}>
            <Switch>
                <Route exact path={["/p/r/incident", "/p/r/incident/:serviceId",
                    "/nav/r/incident", "/nav/r/incident/:serviceId",
                    "/nav/p/r/incident", "/nav/p/r/incident/:serviceId"]}>
                    <IncidentComponent />
                </Route>
                <Route path="/">
                    <h3>incorrect wiring</h3>
                </Route>
            </Switch>
        </BrowserRouter>
    </EccoTheme> ),
    document.getElementById("appbar"));
