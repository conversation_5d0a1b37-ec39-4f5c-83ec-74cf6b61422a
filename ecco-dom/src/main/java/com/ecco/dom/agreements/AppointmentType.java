package com.ecco.dom.agreements;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

import javax.persistence.*;

import com.ecco.config.dom.ListDefinitionEntry;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Type;

import com.ecco.dom.Service;
import com.ecco.infrastructure.entity.IdName;
import com.google.common.base.Predicate;

@Setter
@Getter
@Entity
@Table(name = "appointmenttypes")
public class AppointmentType extends IdName {

    private static final long serialVersionUID = 1L;

    public static final class IsDefault implements Predicate<AppointmentType> {
        @Override
        public boolean apply(AppointmentType input) {
            return input.isDefault();
        }
    }

    @ManyToOne(fetch=FetchType.LAZY)
    @JoinColumn(name="serviceId", nullable=false)
    private Service service;

    @Column
    private boolean isDefault;

    @ManyToMany(fetch=FetchType.EAGER)
    @JoinTable(name = "svccfg_appttype_required",
            joinColumns = @JoinColumn(name = "appointmentTypeId"),
            inverseJoinColumns = @JoinColumn(name = "requirementId"))
    Set<ListDefinitionEntry> requirements;

    //boolean isFlexible;

    @Column(name = "recommendedDuration")
    private int recommendedDurationInMinutes;

    @Column // TODO: Check this for Hib 6 https://www.baeldung.com/hibernate-persist-json-object
    @Type(type = "com.ecco.infrastructure.hibernate.JSONUserTypeStringToObjectMap")
    private Map<String, Object> parameters = new HashMap<>();



}
