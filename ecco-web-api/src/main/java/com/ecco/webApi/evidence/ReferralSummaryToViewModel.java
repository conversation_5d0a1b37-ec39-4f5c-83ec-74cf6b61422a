package com.ecco.webApi.evidence;

import com.ecco.dao.ReferralSummary;
import com.ecco.servicerecipient.ServiceRecipientCaseStatusView.Support;
import com.ecco.servicerecipient.AcceptState;
import com.ecco.calendar.core.util.DateTimeUtils;

import java.util.function.Function;

public final class ReferralSummaryToViewModel implements Function<ReferralSummary, ReferralSummaryViewModel> {

    @Override
    public ReferralSummaryViewModel apply(ReferralSummary input) {
        if (input == null) {
            throw new NullPointerException("input ReferralSummary must not be null - is it soft-deleted?");
        }

        // ******
        // MATCHES ReferralSummaryViewModel properties, except for:
        //
        // not used client side, and not really wanted, so not populated:
        // parentReferralId, accommodation, agencyName
        //
        // receivingService and acceptedReferral are part of the status
        // and do not have properties client-side
        // ******

        ReferralSummaryViewModel result = new ReferralSummaryViewModel();

        // NB we did have these commented from copying over
        //result.parentId = input.referralId;
        //result.textMap = input.getTextMap();
        //NOTE: Don't map currentTaskIndex and currentTaskDefId - as they're not to be used client side
        input.copy(result);

        result._readOnly = input._readOnly;
        result.referralId = input.referralId;
        result.referralCode = input.referralCode;
        result.parentServiceRecipientId = input.parentServiceRecipientId; // FIXME: Think this is used for family support relationships, but duplicates primaryReferralId
        // ReferralToViewModel already covers this
        result.primaryReferralId = input.primaryReferralId;
        result.primaryRelationshipId = input.primaryRelationshipId;

        result.srcGeographicAreaId = input.srcGeographicAreaId;

        result.clientId = input.clientId;
        result.clientCode = input.clientCode;
        result.clientDisplayName = input.clientDisplayName;
        result.displayName = input.clientDisplayName;

        result.clientFirstName = input.firstName;
        result.firstName = input.firstName;
        result.clientLastName = input.lastName;
        result.lastName = input.lastName;

//        if (input.getDateMap() != null) {
//            Map<String, String> view = Maps.transformValues(input.getDateMap(), localDateToString);
//            result.dateMap = new HashMap<String, String>(view);
//        }
//
//        if (input.getChoicesMap() != null) {
//            result.choicesMap = Maps.transformValues(input.getChoicesMap(), choicesMapToViewModel);
//        }
//
//        result.referralReason = input.getReferralReason();
//
//        if (input.getPrimaryReferral() != null) {
//            result.primaryReferralId = input.getPrimaryReferral().getId();
//            if (result.relationshipToPrimary != null) {
//                result.relationshipToPrimary = input.getRelationshipToPrimaryReferral().getName();
//            }
//        }
//
//        if (input.getParentReferral() != null) {
//            result.parentReferralId = input.getParentReferral().getId();
//        }
//

        // source of referral
        result.selfReferral = input.selfReferral;
        result.referrerAgencyId = input.referrerAgencyId;
        result.referrerIndividualId = input.referrerIndividualId;
        result.referralReason = input.referralReason;

        result.pendingStatusId = input.getPendingStatusId();
        result.latestClientStatusId = input.latestClientStatusId;
        result.latestClientStatusDateTime = input.latestClientStatusDateTime;
        result.latestClientStatusOkayDateTime = input.latestClientStatusOkayDateTime;

//        if (input.getCurrentAccommodation() != null) {
//            result.accommodationCategory = input.getCurrentAccommodation().getName();
//        }
//
//        if (input.getFundingSource() != null) {
//            result.fundingSource = input.getFundingSource().getName();
//        }
//        result.fundingPaymentRef = input.getFundingPaymentRef();

//        Agency delivererAgency = input.getDeliveredBy();
//        if (delivererAgency != null) {
//            result.delivererAgencyName = delivererAgency.getName();
//        }

        // TODO Use the locale setting of the authenticated Web API user.
        // (awaiting ECCO-54)
        result.receivedDate = input.receivedDate;

        if (input.dataProtectionAgreementDate != null) {
            result.dataProtectionAgreementStatus = input.getDataProtectionAgreementStatus();
            result.dataProtectionAgreementDate = input.dataProtectionAgreementDate.toLocalDateTime();
            result.dataProtectionSignedId = input.dataProtectionSignedId;
        }

        if (input.consentAgreementDate != null) {
            result.consentAgreementStatus = input.getConsentAgreementStatus();
            result.consentAgreementDate = input.consentAgreementDate.toLocalDateTime();
            result.consentSignedId = input.consentSignedId;
        }

        if (input.agreement1AgreementDate != null) {
            result.agreement1AgreementStatus = input.getAgreement1AgreementStatus();
            result.agreement1AgreementDate = input.agreement1AgreementDate.toLocalDateTime();
            result.agreement1SignedId = input.agreement1SignedId;
        }
        if (input.agreement2AgreementDate != null) {
            result.agreement2AgreementStatus = input.getAgreement2AgreementStatus();
            result.agreement2AgreementDate = input.agreement2AgreementDate.toLocalDateTime();
            result.agreement2SignedId = input.agreement2SignedId;
        }
        if (input.agreement3AgreementDate != null) {
            result.agreement3AgreementStatus = input.getAgreement3AgreementStatus();
            result.agreement3AgreementDate = input.agreement3AgreementDate.toLocalDateTime();
            result.agreement3SignedId = input.agreement3SignedId;
        }
        if (input.agreement4AgreementDate != null) {
            result.agreement4AgreementStatus = input.getAgreement4AgreementStatus();
            result.agreement4AgreementDate = input.agreement4AgreementDate.toLocalDateTime();
            result.agreement4SignedId = input.agreement4SignedId;
        }
        if (input.agreement5AgreementDate != null) {
            result.agreement5AgreementStatus = input.getAgreement5AgreementStatus();
            result.agreement5AgreementDate = input.agreement5AgreementDate.toLocalDateTime();
            result.agreement5SignedId = input.agreement5SignedId;
        }
        if (input.agreement6AgreementDate != null) {
            result.agreement6AgreementStatus = input.getAgreement6AgreementStatus();
            result.agreement6AgreementDate = input.agreement6AgreementDate.toLocalDateTime();
            result.agreement6SignedId = input.agreement6SignedId;
        }
        if (input.agreement7AgreementDate != null) {
            result.agreement7AgreementStatus = input.getAgreement7AgreementStatus();
            result.agreement7AgreementDate = input.agreement7AgreementDate.toLocalDateTime();
            result.agreement7SignedId = input.agreement7SignedId;
        }
        if (input.agreement8AgreementDate != null) {
            result.agreement8AgreementStatus = input.getAgreement8AgreementStatus();
            result.agreement8AgreementDate = input.agreement8AgreementDate.toLocalDateTime();
            result.agreement8SignedId = input.agreement8SignedId;
        }
        if (input.agreement9AgreementDate != null) {
            result.agreement9AgreementStatus = input.getAgreement9AgreementStatus();
            result.agreement9AgreementDate = input.agreement9AgreementDate.toLocalDateTime();
            result.agreement9SignedId = input.agreement9SignedId;
        }
        if (input.agreement10AgreementDate != null) {
            result.agreement10AgreementStatus = input.getAgreement10AgreementStatus();
            result.agreement10AgreementDate = input.agreement10AgreementDate.toLocalDateTime();
            result.agreement10SignedId = input.agreement10SignedId;
        }

        // The audit has date and time, but we mimic 'decisionMadeOn'
        // TODO Also this should really refer to PredicateSupport
        // system date - needs translating into user time zone, but only date based for now
        if (input.firstResponseMadeOn != null) {
            result.firstResponseMadeOn = input.firstResponseMadeOn.toLocalDate();
        }

        // user date - display as-is
        if (input.firstOfferedInterviewDate != null) {
            result.firstOfferedInterviewDate = input.firstOfferedInterviewDate.toLocalDateTime();
        }

        result.interviewDna = input.interviewDna;
        result.interviewDnaComments = input.interviewDnaComments;
        result.interviewSetupComments = input.interviewSetupComments;

        AcceptState[] states = Support.acceptedStates(input.isFinalDecision(), input.getDecisionReferralMadeOn() != null, input.isAcceptedReferral(), input.isAcceptedOnService());
        result.appropriateReferralState = states[0];
        result.acceptOnServiceState = states[1];

        // user date - display as-is
        if (input.decisionDate != null) {
            result.decisionDate = input.decisionDate.toLocalDateTime();
        }

        // TODO Use the locale setting of the authenticated Web API user.
        // (awaiting ECCO-54)
        // system date - needs translating into user time zone, but only date based for now
        // Now that users can change this date, we need to covert correctly back to the users timezone.
        // A stored UTC's datetime of '14-May-2020 23:00:00' is '15th May 2020' to the user
        // but if we use 'toLocalDate' on the UTC date it comes back as '14-May-2020'.
        // This is highlighted bu the use of date only, meaning midnight is used and highlights the problem.
        result.decisionMadeOn = DateTimeUtils.convertFromUtcToUsersLocalDate(input.decisionMadeOn);
        // TODO Use the locale setting of the authenticated Web API user.
        // (awaiting ECCO-54)
        // system date - needs translating into user time zone, but only date based for now
        result.decisionReferralMadeOn = DateTimeUtils.convertFromUtcToUsersLocalDate(input.decisionReferralMadeOn);

        // user date - display as-is
        result.receivingServiceDate = input.receivingServiceDate;

        result.statusMessageKey = Support.getStatusMessageKey(input);
        result.daysAttending = input.daysAttending;
        result.requestedDelete = input.isRequestedDelete();

        result.interviewer1ContactId = input.interviewer1ContactId;
        result.interviewer2ContactId = input.interviewer2ContactId;
        result.interviewLocation = input.interviewLocation;
        result.supportWorkerId = input.supportWorkerId;
        result.exitReasonId = input.exitReasonId;

        // TODO Use the locale setting of the authenticated Web API user.
        // (awaiting ECCO-54)
        // user date - display as-is
        if (input.exitedDate != null) {
            result.exitedDate = input.getExited().toLocalDate();
        }

        result.nextDueSlaDate = input.getNextDueSlaDate();
        result.nextDueSlaTaskId = input.getNextDueSlaTaskId();

        // signposting at referral or service
        // {@see ReferralStatusCommonPredicates#signposted}
        // {@see Referral}
        // signpostedCommentId always means signposted at some point
        result.signpostedCommentId = input.signpostedCommentId;
        result.signpostedBack = input.signpostedBack;
        result.signpostedAgencyId = input.signpostedAgencyId;
        result.signpostedReasonId = input.signpostedReasonId;
        // signposting

        result.fundingSourceId = input.fundingSourceId;
        // user date - display as-is
        if (input.getDecisionFundingDate() != null) {
            result.fundingDecisionDate = input.getDecisionFundingDate().toLocalDate();
        }
        result.fundingHoursOfSupport = input.getFundingHoursOfSupport();
        result.fundingAccepted = input.isAcceptedFunding();

        result.waitingListScore = input.getWaitingListScore();
        return result;
    }

}
