package com.ecco.webApi.clients;

import com.ecco.infrastructure.rest.hateoas.schema.JsonSchemaMetadata;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.springframework.hateoas.RepresentationModel;

import javax.validation.constraints.NotNull;

import static lombok.AccessLevel.PROTECTED;


/**
 * External incident API - creates an incident without a login.
 * Available at /context/api/inbound/incidents/$schema/
 */

@NoArgsConstructor(access = PROTECTED)
@AllArgsConstructor
@Getter
@Builder
public class InboundIncidentResource extends RepresentationModel<InboundIncidentResource> {

    @NotNull
    private Long serviceTypeId;

    @NotNull
    private Integer serviceCategorisationId;

    @JsonSchemaMetadata(title = "category", order = 20)
    private Integer categoryId;

    @JsonSchemaMetadata(title = "reportedById", order = 22)
    private Long reportedById;

    @JsonSchemaMetadata(title = "reportedBy", order = 24)
    private String reportedBy;

    private String reportedByContact;

    @JsonSchemaMetadata(title = "hospitalisationInvolved", order = 30)
    private Boolean hospitalisationInvolved;

    @JsonSchemaMetadata(title = "emergencyServicesInvolved", order = 40)
    private Boolean emergencyServicesInvolved;

}
