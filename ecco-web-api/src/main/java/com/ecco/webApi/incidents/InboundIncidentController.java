package com.ecco.webApi.incidents;

import com.ecco.calendar.core.util.DateTimeUtils;
import com.ecco.dto.ChangeViewModel;
import com.ecco.infrastructure.rest.hateoas.schema.SchemaProvidingController;
import com.ecco.repositories.incidents.IncidentRepository;
import com.ecco.serviceConfig.repositories.ServiceCategorisationRepository;
import com.ecco.webApi.clients.InboundIncidentParams;
import com.ecco.webApi.clients.InboundIncidentResource;
import com.ecco.webApi.contacts.ClientWebService;
import com.ecco.webApi.evidence.BaseCommandViewModel;
import com.ecco.webApi.evidence.EvidenceFormSnapshotCommandViewModel;
import com.ecco.webApi.evidence.EvidenceFormWorkController;
import com.ecco.webApi.evidence.EvidenceParams;
import com.ecco.webApi.viewModels.Result;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.module.jsonSchema.JsonSchema;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.joda.time.Instant;
import org.springframework.composed.web.rest.json.GetJson;
import org.springframework.composed.web.rest.json.PostJson;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.WebRequest;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import java.io.IOException;
import java.net.URI;
import java.util.Optional;
import java.util.UUID;

import static com.ecco.service.security.RunAsTemplate.runAsExternalUserAccountIfNecessary;

/**
 * Controller for allowing public
 */
@RestController
//@PreAuthorize("permitAll()") // - if we do this things blow up on getEntityTypeName expecting an authentication object
@RequestMapping("/inbound/incidents")
@RequiredArgsConstructor
@Slf4j
public class InboundIncidentController extends SchemaProvidingController<InboundIncidentController> {

    private final ServiceCategorisationRepository serviceCategorisationRepository;
    @PersistenceContext
    private EntityManager entityManager;

    private final ClientWebService clientWebService;
    private final IncidentController incidentController;
    private final IncidentRepository incidentRepository;
    private final EvidenceFormWorkController formController;
    private final ObjectMapper objectMapper;

    @Override
    public String getEntityTypeName() {
        return "inbound-incidents";
    }

    @Override
    @GetJson("/$schema/")
    public ResponseEntity<JsonSchema> describe(WebRequest request) {
        JsonSchema schema = getSchemaCreator().create(InboundIncidentResource.class,
                self().describe(request),
                Optional.empty(),
                Optional.of(self().createIncidentWrapper(null)));

        return ResponseEntity.ok(schema);
    }

    @PostJson
    public ResponseEntity<Result> createIncidentWrapper(@RequestBody InboundIncidentParams params) {

        return runAsExternalUserAccountIfNecessary(() -> {
            try {
                return createIncident(params);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        });

    }

    @Nonnull
    ResponseEntity<Result> createIncident(@RequestBody InboundIncidentParams params) throws IOException {
        InboundIncidentResource incident = params.getDto();
        Result result = createIncidentOnly(incident);

        // NB we need to clear - else we don't get a full serviceRecipient at FormUpdateCommandHandler:85 (addEntry)
        // leaving the referral property empty, causing an error on the handler for the withContact() during newSnapshotBuilder
        entityManager.clear();

        try {
            this.saveIncidentDetails(Integer.parseInt(result.getId()), params.getFormDefinitionUuid(), params.getFormData());
        } catch (IOException e) {
            return ResponseEntity.badRequest().body(new Result("failed to save incident details for: " + result));
        }

        // TODO errors??
        return ResponseEntity.created(URI.create("/api/inbound/incidents/")).body(result);
    }

    private Result createIncidentOnly(InboundIncidentResource dto) throws IOException {
        IncidentViewModel i = new IncidentViewModel();

        i.setServiceTypeId(dto.getServiceTypeId());

        // if provided, although one does get set in IncidentFromViewModel
        i.setServiceAllocationId(dto.getServiceCategorisationId());
        i.setReceivedDate(java.time.LocalDate.now());

        i.setCategoryId(dto.getCategoryId());
        i.setReportedById(dto.getReportedById());
        i.setReportedBy(dto.getReportedBy());
        i.setReportedByContact(dto.getReportedByContact());
        i.setEmergencyServicesInvolved(dto.getEmergencyServicesInvolved());
        i.setHospitalisationInvolved(dto.getHospitalisationInvolved());

        // NB creates the incident and its audit
        //clientWebService.create(i);
        //var result = clientWebService.create(i);
        //long incidentId = parseLong(result.getId());

        // NB creates the serviceRecipient and its audit
        return incidentController.createImport(i);
    }

    private void saveIncidentDetails(int incidentId, @Nullable UUID formDefinitionUuid, JsonNode jsonPatch) throws IOException {

        if (formDefinitionUuid == null) {
            log.info("InboundIncidentController: skipping formdefinition empty");
            return;
        }
        // we do a check on the value type only - and assume that Array/Object etc mean data exists
        if (jsonPatch == null || (jsonPatch.isValueNode() && jsonPatch.asText().isEmpty()) || jsonPatch.isNull() || jsonPatch.asText().equals("null")) {
            log.info("InboundIncidentController: skipping jsonPatch as empty: " + objectMapper.writeValueAsString(jsonPatch));
            return;
        }

        int serviceRecipientId = incidentRepository.getServiceRecipientId(incidentId);

        UUID workUuid = UUID.randomUUID();
        EvidenceFormSnapshotCommandViewModel vm = new EvidenceFormSnapshotCommandViewModel(BaseCommandViewModel.OPERATION_ADD,
                serviceRecipientId, "referralDetails", "referralDetails", formDefinitionUuid);
        vm.workUuid = workUuid;
        vm.timestamp = new Instant();
        vm.workDate = ChangeViewModel.changeNullTo(DateTimeUtils.convertFromUtcToUsersLocalDateTime(new DateTime()));
        vm.jsonPatch = jsonPatch;

        String jsonViewModel = objectMapper.writeValueAsString(vm);
        //{"commandName":"formUpdate","commandUri":"service-recipients/200641/evidence/json/referralDetails/referralDetails/","uuid":"f39630cc-af3f-4d64-7a61-ddaa345ea062","operation":"add","timestamp":"2018-02-08T15:36:29.076Z","serviceRecipientId":200641,"evidenceGroup":"referralDetails","taskName":"referralDetails","workUuid":"993c0f9e-4127-4312-769c-d5bd76a123a8","workDate":{"from":null,"to":"2018-02-08T15:36:29.073"},"jsonPatch":[{"op":"replace","path":"/medication/0/name","value":"save in Task.referralDetails4"}]}

        EvidenceParams params = new EvidenceParams();
        params.serviceRecipientId = serviceRecipientId;
        params.taskName = "referralDetails";
        params.evidenceGroupKey = "referralDetails";

        formController.patchSnapshot(SecurityContextHolder.getContext().getAuthentication(), params, jsonViewModel);
    }

}
