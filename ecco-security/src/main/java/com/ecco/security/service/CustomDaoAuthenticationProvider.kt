package com.ecco.security.service

import com.ecco.security.dom.User
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken
import org.springframework.security.authentication.dao.DaoAuthenticationProvider
import org.springframework.security.core.AuthenticationException
import org.springframework.security.core.userdetails.UserDetails
import org.springframework.security.crypto.password.PasswordEncoder

/**
 * An authentication provider that supports additional functionality including:
 *  * IP blacklisting
 *  * password failure lockout
 *  * maintaining a 'last logged in' timestamp
 *
 */
class CustomDaoAuthenticationProvider(
    passwordEncoder: PasswordEncoder?,
    private val userManagementService: UserManagementService,
    private val additionalAuthenticationCheck: AuthenticationCheck<User, UsernamePasswordAuthenticationToken>,
) : DaoAuthenticationProvider() {
    private val log: Logger = LoggerFactory.getLogger(javaClass)

    init {
        setPasswordEncoder(passwordEncoder)
        userDetailsService = userManagementService
    }

    @Throws(AuthenticationException::class)
    override fun additionalAuthenticationChecks(userDetails: UserDetails, authentication: UsernamePasswordAuthenticationToken) {
        val user = userDetails as User

        log.debug("additional authentication checks in progress")

        additionalAuthenticationCheck.check(user, authentication)

        user.markAsSuccessfullyLoggedIn()

        // TODO: Assert we're not in a transaction here
        try {
            userManagementService.updateUser(user)
        } catch (e: Exception) {
            log.warn("Skipping failure of userMgtService.updateUser() on successful login: {}", e.message)
        }
    }
}