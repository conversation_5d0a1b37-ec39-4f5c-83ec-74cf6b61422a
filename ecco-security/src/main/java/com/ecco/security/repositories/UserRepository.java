package com.ecco.security.repositories;

import com.ecco.infrastructure.spring.data.QueryDslPredicateAndProjectionExecutor;
import com.ecco.security.dom.GroupMember;
import com.ecco.security.dom.User;
import org.joda.time.DateTime;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import org.jspecify.annotations.Nullable;
import java.util.List;


public interface UserRepository extends QueryDslPredicateAndProjectionExecutor<User, Long> {

    Integer countByLastLoggedInGreaterThanAndEnabledIsTrue(DateTime dateTime);

    User getByUsername(String username);

    @Query("select u.username from User u WHERE u.contact.email = ?1")
    @Nullable
    String findUsernameByContactEmail(String email);

    // simpler version of findIndividualsFromContactId
    @Query("from User u WHERE u.contact.id = ?1")
    @Nullable
    User findByContactId(long contactId);

    @Query("select u.id from User u WHERE u.contact.id in (?1)")
    List<Long> findUserIdsByContactIds(List<Long> contactId);

    @Query("from User u WHERE lower(u.contact.email) = lower(?1)")
    @Nullable
    List<User> findByContactEmail(String email);

    @Query("from User u WHERE u.oAuthId = ?1")
    @Nullable
    User findByOAuthId(String oAuthId);

    @Modifying
    @Query("UPDATE User SET oAuthId = :oAuthId where id = :id")
    void updateOAuthId(@Param("id") Long id, @Param("oAuthId") String oAuthId);

    @Query("from GroupMember m where m.member.id = ?1")
    List<GroupMember> findUserGroups(long userId);
}
