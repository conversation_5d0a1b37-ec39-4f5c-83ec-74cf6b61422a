package com.ecco.security.service;

import com.azure.spring.aad.webapp.GraphClient;
import com.azure.spring.aad.webapp.GroupInformation;
import com.azure.spring.autoconfigure.aad.AADAuthenticationProperties;
import com.azure.spring.autoconfigure.aad.AADTokenClaim;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.client.oidc.userinfo.OidcUserRequest;
import org.springframework.security.oauth2.client.oidc.userinfo.OidcUserService;
import org.springframework.security.oauth2.client.registration.ClientRegistration;
import org.springframework.security.oauth2.client.userinfo.OAuth2UserRequest;
import org.springframework.security.oauth2.client.userinfo.OAuth2UserService;
import org.springframework.security.oauth2.core.AbstractOAuth2Token;
import org.springframework.security.oauth2.core.OAuth2AccessToken;
import org.springframework.security.oauth2.core.OAuth2AuthenticationException;
import org.springframework.security.oauth2.core.oidc.OidcIdToken;
import org.springframework.security.oauth2.core.oidc.user.DefaultOidcUser;
import org.springframework.security.oauth2.core.oidc.user.OidcUser;
import org.springframework.util.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpSession;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.azure.spring.autoconfigure.aad.Constants.*;


/**
 * Clone of AADOAuth2UserService as of spring-boot 3.4.0 except for 'CUSTOM CODE' entries
 * This is simply to change the user-group: allowed-groups: in azure-scopes,
 * as we can't specify user-group as they are dynamic projects/services.
 * (NB The user-group triggers the required 'Directory.Read.All' permission in AADWebAppConfiguration)
 * We could change AADAuthenticationProperties but its a provided bean that is fairly fixed.
 */
@Slf4j
public class EccoAADOAuth2UserService implements OAuth2UserService<OidcUserRequest, OidcUser> {
    private final OidcUserService oidcUserService;
    private final List<String> allowedGroupNames;
    private final Set<String> allowedGroupIds;
    private final boolean enableFullList;
    private final String azureGroupPrefix; // CUSTOM-CODE
    private final GraphClient graphClient;
    private static final String DEFAULT_OIDC_USER = "defaultOidcUser";
    private static final String ROLES = "roles";

    public EccoAADOAuth2UserService(AADAuthenticationProperties properties, String azureGroupPrefix) {
        this(properties, new GraphClient(properties), azureGroupPrefix);
    }

    public EccoAADOAuth2UserService(AADAuthenticationProperties properties, GraphClient graphClient, String azureGroupPrefix) {
        allowedGroupNames = Optional.ofNullable(properties)
                .map(AADAuthenticationProperties::getUserGroup)
                .map(AADAuthenticationProperties.UserGroupProperties::getAllowedGroupNames)
                .orElseGet(Collections::emptyList);
        allowedGroupIds = Optional.ofNullable(properties)
                .map(AADAuthenticationProperties::getUserGroup)
                .map(AADAuthenticationProperties.UserGroupProperties::getAllowedGroupIds)
                .orElseGet(Collections::emptySet);
        enableFullList = Optional.ofNullable(properties)
                .map(AADAuthenticationProperties::getUserGroup)
                .map(AADAuthenticationProperties.UserGroupProperties::getEnableFullList)
                .orElse(false);
        this.azureGroupPrefix = azureGroupPrefix;
        this.oidcUserService = new OidcUserService();
        this.graphClient = graphClient;
    }

    @Override
    public OidcUser loadUser(OidcUserRequest userRequest) throws OAuth2AuthenticationException {
        // Delegate to the default implementation for loading a user
        OidcUser oidcUser = oidcUserService.loadUser(userRequest);
        OidcIdToken idToken = oidcUser.getIdToken();
        Set<String> authorityStrings = new HashSet<>();
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        ServletRequestAttributes attr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        HttpSession session = attr.getRequest().getSession(true);

        if (authentication != null) {
            return (DefaultOidcUser) session.getAttribute(DEFAULT_OIDC_USER);
        }

        authorityStrings.addAll(extractRolesFromIdToken(idToken));
        authorityStrings.addAll(extractGroupRolesFromAccessToken(userRequest.getAccessToken()));
        // CUSTOM CODE
        // allow us to add further groups based on a prefix
        // NB if we need both rolesFromAccess and rolePrefixesFromAccess this we should share the API call to - graphClient::getGroupsFromGraph
        // NB this could be part of 'extractGroupRolesFromAccessToken' but they differ in that ours has
        //  - no assumption of pre-defined 'allowedGroupNames'
        //  - no need to prefix with ROLE_, unless we change our ldapgroupmapping
        if (azureGroupPrefix != null) {
            GroupInformation groupInformation = getGroupInformation(userRequest.getAccessToken());
            authorityStrings.addAll(extractGroupRolePrefixesFromAccessToken(groupInformation));
        }
        // CUSTOM CODE

        Set<SimpleGrantedAuthority> authorities = authorityStrings.stream()
                .map(SimpleGrantedAuthority::new)
                .collect(Collectors.toSet());

        if (authorities.isEmpty()) {
            authorities = DEFAULT_AUTHORITY_SET;
        }
        String nameAttributeKey =
                Optional.of(userRequest)
                        .map(OAuth2UserRequest::getClientRegistration)
                        .map(ClientRegistration::getProviderDetails)
                        .map(ClientRegistration.ProviderDetails::getUserInfoEndpoint)
                        .map(ClientRegistration.ProviderDetails.UserInfoEndpoint::getUserNameAttributeName)
                        .filter(StringUtils::hasText)
                        .orElse(AADTokenClaim.NAME);
        // Create a copy of oidcUser but use the mappedAuthorities instead
        DefaultOidcUser defaultOidcUser = new DefaultOidcUser(authorities, idToken, nameAttributeKey);
        session.setAttribute(DEFAULT_OIDC_USER, defaultOidcUser);

        return defaultOidcUser;
    }

    Set<String> extractRolesFromIdToken(OidcIdToken idToken) {
        Set<String> roles = Optional.ofNullable(idToken)
                .map(token -> (Collection<?>) token.getClaim(ROLES))
                .filter(obj -> obj instanceof List<?>)
                .map(Collection::stream)
                .orElseGet(Stream::empty)
                .filter(s -> StringUtils.hasText(s.toString()))
                .map(role -> APPROLE_PREFIX + role)
                .collect(Collectors.toSet());
        return roles;
    }

    Set<String> extractGroupRolesFromAccessToken(OAuth2AccessToken accessToken) {
        if (allowedGroupNames.isEmpty() && allowedGroupIds.isEmpty()) {
            return Collections.emptySet();
        }
        Set<String> roles = new HashSet<>();
        GroupInformation groupInformation = getGroupInformation(accessToken);
        if (!allowedGroupNames.isEmpty()) {
            Optional.of(groupInformation)
                    .map(GroupInformation::getGroupsNames)
                    .map(Collection::stream)
                    .orElseGet(Stream::empty)
                    .filter(allowedGroupNames::contains)
                    .forEach(roles::add);
        }
        if (!allowedGroupIds.isEmpty()) {
            Optional.of(groupInformation)
                    .map(GroupInformation::getGroupsIds)
                    .map(Collection::stream)
                    .orElseGet(Stream::empty)
                    .filter(this::isAllowedGroupId)
                    .forEach(roles::add);
        }
        return roles.stream()
                .map(roleStr -> ROLE_PREFIX + roleStr)
                .collect(Collectors.toSet());
    }

    private boolean isAllowedGroupId(String groupId) {
        if (enableFullList) {
            return true;
        }
        if (allowedGroupIds.size() == 1 && allowedGroupIds.contains("all")) {
            return true;
        }
        return allowedGroupIds.contains(groupId);
    }

    private GroupInformation getGroupInformation(OAuth2AccessToken accessToken) {
        return Optional.of(accessToken)
                .map(AbstractOAuth2Token::getTokenValue)
                .map(graphClient::getGroupInformation)
                .orElseGet(GroupInformation::new);
    }

    // CUSTOM CODE
    Set<String> extractGroupRolePrefixesFromAccessToken(GroupInformation groupInformation) {
        Set<String> roles = Optional.of(groupInformation)
                .map(GroupInformation::getGroupsNames)
                .map(Collection::stream)
                .orElseGet(Stream::empty)
                .map(this::matchGroupPrefix)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        log.debug("OAuth2 incoming groups matched: {}", roles);
        return roles;
    }
    String matchGroupPrefix(String group) {
        return group.toLowerCase().startsWith(azureGroupPrefix.toLowerCase())
                ? group : null;
    }
    // CUSTOM CODE

}