package com.ecco.security.service;

import com.azure.spring.autoconfigure.aad.AADAuthenticationProperties;
import com.ecco.security.dom.User;
import com.ecco.security.repositories.UserRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.authentication.DisabledException;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.oauth2.client.oidc.userinfo.OidcUserRequest;
import org.springframework.security.oauth2.client.userinfo.OAuth2UserService;
import org.springframework.security.oauth2.core.OAuth2AuthenticationException;
import org.springframework.security.oauth2.core.oidc.user.OidcUser;
import org.springframework.util.Assert;

import org.jspecify.annotations.Nullable;


@Slf4j
public class EccoEnhancingOidcUserService implements OAuth2UserService<OidcUserRequest, OidcUser> {

    private final Boolean syncUser;
    private final EccoAADOAuth2UserService oidcUserService;
    private final EccoUserDetailsContextMapper userDetailsContextMapper;
    private final UserRepository userRepository;
    private final UserManagementService userManagementService;

    public EccoEnhancingOidcUserService(AADAuthenticationProperties properties,
                                        EccoUserDetailsContextMapper userDetailsContextMapper,
                                        UserRepository userRepository,
                                        UserManagementService userManagementService,
                                        String azureGroupPrefix) {
        this.userRepository = userRepository;
        this.userManagementService = userManagementService;
        this.userDetailsContextMapper = userDetailsContextMapper;
        this.oidcUserService = new EccoAADOAuth2UserService(properties, azureGroupPrefix);
        // if we have groups configured then we expect to create new users and sync on all groups
        // if we do not have groups configured, then we expect users to exist in ecco and ad as auth only
        this.syncUser = azureGroupPrefix != null || !properties.getUserGroup().getAllowedGroupNames().isEmpty();
        log.info("OAuth2 SETUP to sync user: {}", this.syncUser);
    }

    @Override
    public OidcUser loadUser(OidcUserRequest userRequest) throws OAuth2AuthenticationException {
        var oidcUser = oidcUserService.loadUser(userRequest);

        // find the user using the oAuth identifier
        // and not the email if we can avoid it - see DEV-2496
        String oAuthId = getOAuthId(oidcUser);
        OidcUserWithEccoUser oAuthLocalUser = loadAndSyncUserUsingOAuthId(oidcUser, oAuthId);
        if (oAuthLocalUser != null) return oAuthLocalUser;

        // find the user using the email identifier
        String email = getEmail(oidcUser);
        OidcUserWithEccoUser emailLocalUser = loadAndSyncOrCreateUserUsingEmail(oidcUser, email, oAuthId);
        if (emailLocalUser != null) return emailLocalUser;

        // no user found, so throw an exception

        // we did return the incoming oidcUser below this assert - see 6f85c524 (which we've commented back in for completeness)
        // but since we no longer return the oidcUser, but throw an exception, we don't need this assert
        // however, perhaps we do the assert for sanity that we are at least trying to be a user on this system
        // getAuthorities gets from the loadUser which calls extractGroupRolesFromAccessToken - which expects us to have defined allowedGroups
        // the groups themselves are got from an additional call using getUserMemberships where the url is from "azure.activedirectory"
        // the property is graphMembershipUri, so we need "azure.activedirectory.graph-membership-uri".
        // By default, this is https://graph.microsoft.com/v1.0/me/memberOf (according to https://github.com/Azure/azure-sdk-for-java/blob/main/sdk/spring/azure-spring-boot-starter-active-directory/README.md)
        Assert.state(oidcUser.getAuthorities().size() == 1 &&
                oidcUser.getAuthorities().toArray(new GrantedAuthority[1])[0].getAuthority().equals("ROLE_USER"),
                "Default user should only have ROLE_USER and nothing else - for now");
        //return oidcUser;

        // NB non-unique email should throw before gets here
        // if no email found in preferred_username, then can get here
        throw new DisabledException(
                "Error: You cannot sign in with Microsoft unless the email address provided ("+email+") has first been set on your ecco login account");

        //  TODO: Review this.  It probably should never proceed with defaults for client access, as we'll need to look up their client record
        //   return oidcUser;
    }

    private static String getEmail(OidcUser oidcUser) {
        var preferred_email = (String) oidcUser.getClaims().get("preferred_username");
        var actual_email = (String) oidcUser.getClaims().get("email");

        log.info("OAuth2 incoming user claim preferred_username: {}", preferred_email);
        log.info("OAuth2 incoming user email: {}", actual_email);

        return preferred_email != null ? preferred_email : actual_email;
    }

    private static String getOAuthId(OidcUser oidcUser) {
        var oid = oidcUser.getClaims().get("oid");
        var tid = oidcUser.getClaims().get("tid");
        return tid.toString().concat(":").concat(oid.toString());
    }

    private OidcUserWithEccoUser loadAndSyncOrCreateUserUsingEmail(OidcUser oidcUser, String email, String oAuthId) {
        if (email != null) {

            // is email safe to use - check for domain verified
            // see https://learn.microsoft.com/en-us/azure/active-directory/develop/migrate-off-email-claim-authorization
            // NB edov is Email Domain Owner Verified
            var edov = oidcUser.getClaims().get("xms_edov");
            if (edov != null) {
                var domainVerified = Boolean.TRUE.equals(Boolean.parseBoolean(edov.toString()));
                if (!domainVerified) {
                    // NB we could do something ourselves to verify the email - eg OTP? but we just reject
                    throw new OAuth2AuthenticationException("the supplied email is NOT safe to use - the domain is not verified");
                }
            } else {
                log.error("OAuth2 MAYBE VULNERABLE - if edov claim not set on app registrations pre July 2023");
            }

            // if we are syncing/creating a user, jump to this path first
            // but still drop through to update the oAuthId
            if (this.syncUser) {
                createUserWithGroups(oidcUser, email, oAuthId);
            }

            // if simple verification by email...
            var users = userRepository.findByContactEmail(email);
            if (users.size() > 1) {
                throw new OAuth2AuthenticationException("the supplied email is associated with more than one user");
            }
            var user = users.get(0);
            if (user != null) {
                log.info("OAuth incoming user email FOUND: {}", email);
                if (user.getOAuthId() == null) {
                    // TODO wrap in tx
                    //  javax.persistence.TransactionRequiredException: Executing an update/delete query
                    user.setOAuthId(oAuthId);
                    userManagementService.updateUser(user);
                    user = userRepository.findByOAuthId(oAuthId);
                }
                return verifyUser(oidcUser, user);
            }
        }

        return null;
    }

    @Nullable
    private OidcUserWithEccoUser loadAndSyncUserUsingOAuthId(OidcUser oidcUser, String oAuthId) {

        log.info("OAuth2 incoming user oAuthId: {}", oAuthId);

        var oAuthEccoUser = userRepository.findByOAuthId(oAuthId);
        if (oAuthEccoUser != null) {
            log.info("OAuth2 incoming user oAuthId FOUND: {}", oAuthId);
            var u = verifyUser(oidcUser, oAuthEccoUser);

            if (this.syncUser) {
                // the user isn't created because they already exist
                var user = userDetailsContextMapper.findOrCreateUser(oAuthEccoUser.getUsername(), true, oAuthId, null, oidcUser.getAuthorities(), group -> group);
                log.debug("OAuth2 sync: complete for: {}", user.getUsername());
                return new OidcUserWithEccoUser(oidcUser.getIdToken(), oidcUser.getUserInfo(), user);
            }

            return u;
        }
        return null;
    }

    private static OidcUserWithEccoUser verifyUser(OidcUser oidcUser, User oAuthEccoUser) {
        if (!oAuthEccoUser.isEnabled()) {
            throw new DisabledException("Your ECCO account has been disabled");
        }
        return new OidcUserWithEccoUser(oidcUser.getIdToken(), oidcUser.getUserInfo(), oAuthEccoUser);
    }

    private OidcUserWithEccoUser createUserWithGroups(OidcUser oidcUser, String email, String oAuthId) {
        var username = userRepository.findUsernameByContactEmail(email); // TODO throw an error if too many results
        log.info("OAuth2 sync: existing username {}", username);
        if (username == null) {
            username = StringUtils.substringBefore(email, "@");
            log.info("OAuth2 sync: new username {}", username);
        }
        var newUserContactName = oidcUser.getFullName();
        var user = userDetailsContextMapper.findOrCreateUser(username, true, oAuthId, newUserContactName, oidcUser.getAuthorities(), group -> group);
        log.debug("OAuth2 sync: complete for: {}", user.getUsername());
        return new OidcUserWithEccoUser(oidcUser.getIdToken(), oidcUser.getUserInfo(), user);
    }
}